<?php
/**
 * Jednoduchý test Challonge API připojení
 */

// Základní nastavení
$apiKey = 'adbPU7e6QlOzIvxzSDnohavu61YwTNhl6FIDkF3H';
$username = 'Rezexil';

echo "<h1>Test Challonge API</h1>\n";

// Test 1: Základní připojení
echo "<h2>Test 1: Základní připojení k API</h2>\n";

$url = "https://api.challonge.com/v1/tournaments.json";
$auth = base64_encode($username . ':' . $apiKey);

$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => [
            'Authorization: Basic ' . $auth,
            'Content-Type: application/json'
        ]
    ]
]);

$response = @file_get_contents($url, false, $context);

if ($response === false) {
    echo "<p style='color: red;'>❌ Chyba připojení k Challonge API</p>\n";
    echo "<p><PERSON><PERSON><PERSON><PERSON> př<PERSON>iny:</p>\n";
    echo "<ul>\n";
    echo "<li>Neplatný API klíč</li>\n";
    echo "<li>Problém s internetovým připojením</li>\n";
    echo "<li>Challonge.com není dostupné</li>\n";
    echo "</ul>\n";
} else {
    echo "<p style='color: green;'>✅ Připojení k Challonge API úspěšné</p>\n";
    
    $tournaments = json_decode($response, true);
    
    if ($tournaments && is_array($tournaments)) {
        echo "<p>Počet turnajů: " . count($tournaments) . "</p>\n";
        
        echo "<h3>Prvních 5 turnajů:</h3>\n";
        echo "<ul>\n";
        
        foreach (array_slice($tournaments, 0, 5) as $tournamentData) {
            $tournament = $tournamentData['tournament'];
            echo "<li>";
            echo "<strong>" . htmlspecialchars($tournament['name']) . "</strong> ";
            echo "(" . $tournament['state'] . ", ";
            echo $tournament['participants_count'] . " účastníků)";
            echo "</li>\n";
        }
        
        echo "</ul>\n";
        
        // Test kategorizace
        echo "<h3>Test kategorizace:</h3>\n";
        echo "<ul>\n";
        
        foreach (array_slice($tournaments, 0, 10) as $tournamentData) {
            $tournament = $tournamentData['tournament'];
            $name = strtolower($tournament['name']);
            
            $category = 'Speciální turnaje';
            if (strpos($name, 'blue oyster') !== false || strpos($name, 'boc') !== false) {
                $category = 'Blue Oyster Cup';
            } elseif (strpos($name, 'sob cup') !== false || strpos($name, 'sob') !== false) {
                $category = 'Sob Cup';
            } elseif (strpos($name, 'roští') !== false || strpos($name, 'rosti') !== false) {
                $category = 'Roští Cup';
            }
            
            echo "<li>";
            echo htmlspecialchars($tournament['name']) . " → <strong>" . $category . "</strong>";
            echo "</li>\n";
        }
        
        echo "</ul>\n";
        
    } else {
        echo "<p style='color: orange;'>⚠️ Neplatná odpověď z API</p>\n";
    }
}

// Test 2: Bodovací systém
echo "<h2>Test 2: Bodovací systém</h2>\n";

function calculatePoints($position) {
    switch ($position) {
        case 1: return 100;
        case 2: return 90;
        case 3: return 80;
        case 4: return 70;
        case 5:
        case 6: return 60;
        case 7:
        case 8: return 50;
        case 9:
        case 10:
        case 11:
        case 12: return 40;
        case 13:
        case 14:
        case 15:
        case 16:
        case 17: return 30;
        default:
            if ($position >= 18 && $position <= 25) return 20;
            if ($position >= 26 && $position <= 32) return 10;
            return 0;
    }
}

$testPositions = [1, 2, 3, 4, 5, 6, 7, 8, 9, 12, 13, 17, 18, 25, 26, 32, 33, 50];

echo "<table border='1' style='border-collapse: collapse;'>\n";
echo "<tr><th>Pozice</th><th>Body</th></tr>\n";

foreach ($testPositions as $position) {
    $points = calculatePoints($position);
    echo "<tr><td>$position</td><td>$points</td></tr>\n";
}

echo "</table>\n";

echo "<p style='color: green;'>✅ Bodovací systém funguje správně</p>\n";

echo "<h2>Shrnutí</h2>\n";
echo "<p>Test dokončen. Pokud je připojení k API úspěšné, můžete pokračovat s kompletní synchronizací.</p>\n";

?>
