<?php
/**
 * Třída pro komunikaci s Challonge API
 */
class ChallongeAPI {
    private $apiKey;
    private $username;
    private $baseUrl = 'https://api.challonge.com/v1/';

    /**
     * Konstruktor
     *
     * @param string $apiKey API klíč pro Challonge
     * @param string $username Uživatelské jméno pro Challonge
     */
    public function __construct($apiKey, $username) {
        $this->apiKey = $apiKey;
        $this->username = $username;
    }

    /**
     * Získá seznam všech turnajů
     *
     * @param array $params Další parametry pro API
     * @return array Seznam turnajů
     */
    public function getTournaments($params = []) {
        return $this->makeRequest('GET', 'tournaments.json', $params);
    }

    /**
     * Získá detail turnaje
     *
     * @param string $tournamentId ID turnaje
     * @param array $params Další parametry pro API
     * @return array Detail turnaje
     */
    public function getTournament($tournamentId, $params = []) {
        return $this->makeRequest('GET', "tournaments/{$tournamentId}.json", $params);
    }

    /**
     * Získá seznam účastníků turnaje
     *
     * @param string $tournamentId ID turnaje
     * @param array $params Další parametry pro API
     * @return array Seznam účastníků
     */
    public function getParticipants($tournamentId, $params = []) {
        return $this->makeRequest('GET', "tournaments/{$tournamentId}/participants.json", $params);
    }

    /**
     * Získá seznam zápasů turnaje
     *
     * @param string $tournamentId ID turnaje
     * @param array $params Další parametry pro API
     * @return array Seznam zápasů
     */
    public function getMatches($tournamentId, $params = []) {
        return $this->makeRequest('GET', "tournaments/{$tournamentId}/matches.json", $params);
    }

    /**
     * Kategorizuje turnaj podle názvu
     *
     * @param string $tournamentName Název turnaje
     * @return string Kategorie turnaje
     */
    public function categorizeTournament($tournamentName) {
        $name = strtolower($tournamentName);

        if (strpos($name, 'blue oyster') !== false || strpos($name, 'boc') !== false) {
            return 'Blue Oyster Cup';
        }

        if (strpos($name, 'sob cup') !== false || strpos($name, 'sob') !== false) {
            return 'Sob Cup';
        }

        if (strpos($name, 'roští') !== false || strpos($name, 'rosti') !== false) {
            return 'Roští Cup';
        }

        return 'Speciální turnaje';
    }

    /**
     * Vypočítá body podle umístění v turnaji
     *
     * @param int $position Pozice hráče v turnaji
     * @return int Počet bodů
     */
    public function calculatePoints($position) {
        switch ($position) {
            case 1: return 100;
            case 2: return 90;
            case 3: return 80;
            case 4: return 70;
            case 5:
            case 6: return 60;
            case 7:
            case 8: return 50;
            case 9:
            case 10:
            case 11:
            case 12: return 40;
            case 13:
            case 14:
            case 15:
            case 16:
            case 17: return 30;
            default:
                if ($position >= 18 && $position <= 25) return 20;
                if ($position >= 26 && $position <= 32) return 10;
                return 0; // 33+ místo
        }
    }

    /**
     * Provede HTTP požadavek na Challonge API
     *
     * @param string $method HTTP metoda (GET, POST, PUT, DELETE)
     * @param string $endpoint Koncový bod API
     * @param array $params Parametry požadavku
     * @return array Odpověď z API
     */
    private function makeRequest($method, $endpoint, $params = []) {
        $url = $this->baseUrl . $endpoint;

        // Přidání autentizačních údajů
        $params['api_key'] = $this->apiKey;

        // Sestavení URL s parametry pro GET požadavky
        if ($method === 'GET' && !empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        // Nastavení metody
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);

        // Přidání dat pro POST, PUT požadavky
        if ($method !== 'GET' && !empty($params)) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if (curl_errno($ch)) {
            throw new Exception('Curl error: ' . curl_error($ch));
        }

        curl_close($ch);

        // Zpracování odpovědi
        $data = json_decode($response, true);

        if ($httpCode >= 400) {
            $error = isset($data['errors']) ? implode(', ', $data['errors']) : 'Unknown error';
            throw new Exception("API error ({$httpCode}): {$error}");
        }

        return $data;
    }
}

// Konfigurace API
$challongeApiKey = 'adbPU7e6QlOzIvxzSDnohavu61YwTNhl6FIDkF3H';
$challongeUsername = 'Rezexil';

// Vytvoření instance API
$challongeApi = new ChallongeAPI($challongeApiKey, $challongeUsername);
