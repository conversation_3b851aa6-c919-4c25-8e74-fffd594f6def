<?php
/**
 * Skript pro nahrání souborů přes admin rozhraní
 */

// Spuštění session
session_start();

// Kontrola, zda je uživatel přihlášen
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('HTTP/1.1 401 Unauthorized');
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Nejste přihlášen']);
    exit;
}

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

try {
    // Kontrola POST dat
    if (!isset($_POST['filename']) || !isset($_POST['content'])) {
        throw new Exception('Chybí název souboru nebo obsah');
    }
    
    $filename = $_POST['filename'];
    $content = $_POST['content'];
    
    // Kontrola názvu souboru (bezpečnost)
    if (!preg_match('/^[a-zA-Z0-9_-]+\.php$/', $filename)) {
        throw new Exception('Neplatný název souboru');
    }
    
    // Povolené soubory
    $allowedFiles = [
        'update-database-for-challonge.php',
        'test-challonge-sync.php',
        'sync-and-evaluate-tournaments.php',
        'get-challonge-stats.php',
        'weekly-sync-cron.php'
    ];
    
    if (!in_array($filename, $allowedFiles)) {
        throw new Exception('Soubor není povolen pro upload');
    }
    
    // Cesta k souboru
    $filePath = __DIR__ . '/' . $filename;
    
    // Zápis souboru
    $result = file_put_contents($filePath, $content);
    
    if ($result === false) {
        throw new Exception('Nepodařilo se zapsat soubor');
    }
    
    // Nastavení správných oprávnění
    chmod($filePath, 0644);
    
    echo json_encode([
        'success' => true,
        'message' => "Soubor {$filename} byl úspěšně nahrán",
        'filename' => $filename,
        'size' => $result
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
