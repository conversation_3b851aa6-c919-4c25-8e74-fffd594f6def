<?php
/**
 * Skript pro synchronizaci dat z Challonge API
 */

// Připojení k databázi a Challonge API
require_once 'db-connection.php';
require_once 'challonge-api.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

try {
    // Získání seznamu turnajů z Challonge API
    $challongeTournaments = $challongeApi->getTournaments();
    
    if (empty($challongeTournaments)) {
        echo json_encode(['status' => 'success', 'message' => 'Žádné turnaje k synchronizaci']);
        exit;
    }
    
    // Počítadla pro statistiky
    $stats = [
        'tournaments' => [
            'total' => count($challongeTournaments),
            'new' => 0,
            'updated' => 0
        ],
        'participants' => [
            'total' => 0,
            'new' => 0,
            'updated' => 0
        ],
        'matches' => [
            'total' => 0,
            'new' => 0,
            'updated' => 0
        ]
    ];
    
    // Zpracování turnajů
    foreach ($challongeTournaments as $challongeTournament) {
        $tournament = $challongeTournament['tournament'];
        
        // Kontrola, zda turnaj již existuje v databázi
        $stmt = $pdo->prepare("SELECT id, challonge_id FROM tournaments WHERE challonge_id = ?");
        $stmt->execute([$tournament['id']]);
        $existingTournament = $stmt->fetch();
        
        // Určení kategorie turnaje podle názvu
        $categoryId = determineTournamentCategory($tournament['name']);
        
        if ($existingTournament) {
            // Aktualizace existujícího turnaje
            $stmt = $pdo->prepare("
                UPDATE tournaments 
                SET 
                    name = ?,
                    description = ?,
                    category_id = ?,
                    start_date = ?,
                    end_date = ?,
                    url = ?,
                    status = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");
            
            $stmt->execute([
                $tournament['name'],
                $tournament['description'],
                $categoryId,
                $tournament['started_at'] ? date('Y-m-d', strtotime($tournament['started_at'])) : null,
                $tournament['completed_at'] ? date('Y-m-d', strtotime($tournament['completed_at'])) : null,
                $tournament['full_challonge_url'],
                $tournament['state'],
                $existingTournament['id']
            ]);
            
            $tournamentId = $existingTournament['id'];
            $stats['tournaments']['updated']++;
        } else {
            // Vytvoření nového turnaje
            $stmt = $pdo->prepare("
                INSERT INTO tournaments (
                    challonge_id, name, description, category_id, 
                    start_date, end_date, url, status, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            
            $stmt->execute([
                $tournament['id'],
                $tournament['name'],
                $tournament['description'],
                $categoryId,
                $tournament['started_at'] ? date('Y-m-d', strtotime($tournament['started_at'])) : null,
                $tournament['completed_at'] ? date('Y-m-d', strtotime($tournament['completed_at'])) : null,
                $tournament['full_challonge_url'],
                $tournament['state']
            ]);
            
            $tournamentId = $pdo->lastInsertId();
            $stats['tournaments']['new']++;
        }
        
        // Synchronizace účastníků turnaje
        syncTournamentParticipants($tournamentId, $tournament['id'], $stats);
        
        // Synchronizace zápasů turnaje
        syncTournamentMatches($tournamentId, $tournament['id'], $stats);
    }
    
    // Aktualizace času poslední synchronizace
    $stmt = $pdo->prepare("
        INSERT INTO settings (setting_key, setting_value, created_at, updated_at)
        VALUES ('last_sync_time', NOW(), NOW(), NOW())
        ON DUPLICATE KEY UPDATE setting_value = NOW(), updated_at = NOW()
    ");
    $stmt->execute();
    
    // Vrácení statistik synchronizace
    echo json_encode([
        'status' => 'success',
        'message' => 'Synchronizace dokončena',
        'stats' => $stats
    ]);
} catch (Exception $e) {
    // Vrácení chybové zprávy jako JSON
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => 'Chyba při synchronizaci: ' . $e->getMessage()]);
}

/**
 * Určí kategorii turnaje podle názvu
 * 
 * @param string $tournamentName Název turnaje
 * @return int ID kategorie
 */
function determineTournamentCategory($tournamentName) {
    if (stripos($tournamentName, 'Blue Oyster') !== false || stripos($tournamentName, 'BOC') !== false) {
        return 1; // Blue Oyster Cup
    } elseif (stripos($tournamentName, 'Sob Cup') !== false) {
        return 2; // Sob Cup
    } elseif (stripos($tournamentName, 'Roští') !== false || stripos($tournamentName, 'Rosti') !== false) {
        return 3; // Roští Cup
    } else {
        return 4; // Speciální turnaje
    }
}

/**
 * Synchronizuje účastníky turnaje
 * 
 * @param int $tournamentId ID turnaje v databázi
 * @param string $challongeId ID turnaje v Challonge
 * @param array &$stats Statistiky synchronizace
 */
function syncTournamentParticipants($tournamentId, $challongeId, &$stats) {
    global $pdo, $challongeApi;
    
    // Získání účastníků turnaje z Challonge API
    $participants = $challongeApi->getParticipants($challongeId);
    
    if (empty($participants)) {
        return;
    }
    
    $stats['participants']['total'] += count($participants);
    
    foreach ($participants as $participantData) {
        $participant = $participantData['participant'];
        
        // Kontrola, zda hráč již existuje v databázi
        $stmt = $pdo->prepare("SELECT id FROM players WHERE name = ?");
        $stmt->execute([$participant['name']]);
        $player = $stmt->fetch();
        
        if (!$player) {
            // Vytvoření nového hráče
            $stmt = $pdo->prepare("INSERT INTO players (name, created_at, updated_at) VALUES (?, NOW(), NOW())");
            $stmt->execute([$participant['name']]);
            $playerId = $pdo->lastInsertId();
            $stats['participants']['new']++;
        } else {
            $playerId = $player['id'];
        }
        
        // Kontrola, zda účastník již existuje v turnaji
        $stmt = $pdo->prepare("
            SELECT id FROM tournament_participants 
            WHERE tournament_id = ? AND player_id = ?
        ");
        $stmt->execute([$tournamentId, $playerId]);
        $existingParticipant = $stmt->fetch();
        
        if ($existingParticipant) {
            // Aktualizace existujícího účastníka
            $stmt = $pdo->prepare("
                UPDATE tournament_participants 
                SET 
                    challonge_participant_id = ?,
                    seed = ?,
                    final_rank = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");
            
            $stmt->execute([
                $participant['id'],
                $participant['seed'],
                $participant['final_rank'],
                $existingParticipant['id']
            ]);
        } else {
            // Vytvoření nového účastníka
            $stmt = $pdo->prepare("
                INSERT INTO tournament_participants (
                    tournament_id, player_id, challonge_participant_id, 
                    seed, final_rank, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, NOW(), NOW())
            ");
            
            $stmt->execute([
                $tournamentId,
                $playerId,
                $participant['id'],
                $participant['seed'],
                $participant['final_rank']
            ]);
            
            $stats['participants']['updated']++;
        }
    }
}

/**
 * Synchronizuje zápasy turnaje
 * 
 * @param int $tournamentId ID turnaje v databázi
 * @param string $challongeId ID turnaje v Challonge
 * @param array &$stats Statistiky synchronizace
 */
function syncTournamentMatches($tournamentId, $challongeId, &$stats) {
    global $pdo, $challongeApi;
    
    // Získání zápasů turnaje z Challonge API
    $matches = $challongeApi->getMatches($challongeId);
    
    if (empty($matches)) {
        return;
    }
    
    $stats['matches']['total'] += count($matches);
    
    // Získání účastníků turnaje z databáze
    $stmt = $pdo->prepare("
        SELECT tp.player_id, tp.challonge_participant_id 
        FROM tournament_participants tp
        WHERE tp.tournament_id = ?
    ");
    $stmt->execute([$tournamentId]);
    $participants = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    
    foreach ($matches as $matchData) {
        $match = $matchData['match'];
        
        // Získání ID hráčů
        $player1Id = isset($participants[$match['player1_id']]) ? $participants[$match['player1_id']] : null;
        $player2Id = isset($participants[$match['player2_id']]) ? $participants[$match['player2_id']] : null;
        
        // Určení vítěze
        $winnerId = null;
        if ($match['winner_id']) {
            $winnerId = isset($participants[$match['winner_id']]) ? $participants[$match['winner_id']] : null;
        }
        
        // Kontrola, zda zápas již existuje v databázi
        $stmt = $pdo->prepare("
            SELECT id FROM matches 
            WHERE tournament_id = ? AND challonge_match_id = ?
        ");
        $stmt->execute([$tournamentId, $match['id']]);
        $existingMatch = $stmt->fetch();
        
        // Zpracování skóre
        $scorePlayer1 = null;
        $scorePlayer2 = null;
        
        if ($match['scores_csv']) {
            $scores = explode('-', $match['scores_csv']);
            if (count($scores) == 2) {
                $scorePlayer1 = (int) trim($scores[0]);
                $scorePlayer2 = (int) trim($scores[1]);
            }
        }
        
        if ($existingMatch) {
            // Aktualizace existujícího zápasu
            $stmt = $pdo->prepare("
                UPDATE matches 
                SET 
                    round = ?,
                    player1_id = ?,
                    player2_id = ?,
                    score_player1 = ?,
                    score_player2 = ?,
                    winner_id = ?,
                    match_date = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");
            
            $stmt->execute([
                $match['round'],
                $player1Id,
                $player2Id,
                $scorePlayer1,
                $scorePlayer2,
                $winnerId,
                $match['completed_at'] ? date('Y-m-d H:i:s', strtotime($match['completed_at'])) : null,
                $existingMatch['id']
            ]);
            
            $stats['matches']['updated']++;
        } else {
            // Vytvoření nového zápasu
            $stmt = $pdo->prepare("
                INSERT INTO matches (
                    tournament_id, challonge_match_id, round, 
                    player1_id, player2_id, score_player1, score_player2, 
                    winner_id, match_date, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            
            $stmt->execute([
                $tournamentId,
                $match['id'],
                $match['round'],
                $player1Id,
                $player2Id,
                $scorePlayer1,
                $scorePlayer2,
                $winnerId,
                $match['completed_at'] ? date('Y-m-d H:i:s', strtotime($match['completed_at'])) : null
            ]);
            
            $stats['matches']['new']++;
        }
    }
}
