<?php
/**
 * Skript pro získání detailu hráče
 */

// Připojení k databázi
require_once 'db-connection.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

// Kontrola, zda byl zadán ID
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Chybí ID hráče']);
    exit;
}

$playerId = (int) $_GET['id'];

try {
    // Získání základních informací o hráči
    $stmt = $pdo->prepare("SELECT * FROM players WHERE id = ?");
    $stmt->execute([$playerId]);
    $player = $stmt->fetch();
    
    if (!$player) {
        http_response_code(404);
        echo json_encode(['error' => '<PERSON><PERSON><PERSON><PERSON> nebyl nalezen']);
        exit;
    }
    
    // Pokud jsou požadovány statistiky
    if (isset($_GET['stats'])) {
        // Získání statistik hráče
        $statsQuery = "
            SELECT 
                COUNT(DISTINCT tp.tournament_id) AS tournaments,
                COUNT(DISTINCT CASE WHEN m.winner_id = ? THEN m.id END) AS wins,
                COUNT(DISTINCT CASE WHEN (m.player1_id = ? OR m.player2_id = ?) AND m.winner_id != ? AND m.winner_id IS NOT NULL THEN m.id END) AS losses,
                COUNT(DISTINCT CASE WHEN (m.player1_id = ? OR m.player2_id = ?) THEN m.id END) AS matches
            FROM 
                players p
            LEFT JOIN 
                tournament_participants tp ON p.id = tp.player_id
            LEFT JOIN 
                matches m ON (m.player1_id = p.id OR m.player2_id = p.id)
            WHERE 
                p.id = ?
        ";
        
        $statsStmt = $pdo->prepare($statsQuery);
        $statsStmt->execute([$playerId, $playerId, $playerId, $playerId, $playerId, $playerId, $playerId]);
        $stats = $statsStmt->fetch();
        
        $player['stats'] = $stats;
    }
    
    // Pokud jsou požadovány zápasy
    if (isset($_GET['matches'])) {
        // Získání zápasů hráče
        $matchesQuery = "
            SELECT 
                m.*,
                t.name AS tournament_name,
                p1.name AS player1_name,
                p2.name AS player2_name
            FROM 
                matches m
            JOIN 
                tournaments t ON m.tournament_id = t.id
            LEFT JOIN 
                players p1 ON m.player1_id = p1.id
            LEFT JOIN 
                players p2 ON m.player2_id = p2.id
            WHERE 
                m.player1_id = ? OR m.player2_id = ?
            ORDER BY 
                m.match_date DESC, m.id DESC
        ";
        
        $matchesStmt = $pdo->prepare($matchesQuery);
        $matchesStmt->execute([$playerId, $playerId]);
        $matches = $matchesStmt->fetchAll();
        
        $player['matches'] = $matches;
    }
    
    // Vrácení detailu hráče jako JSON
    echo json_encode($player);
} catch (PDOException $e) {
    // Vrácení chybové zprávy jako JSON
    http_response_code(500);
    echo json_encode(['error' => 'Nepodařilo se získat detail hráče: ' . $e->getMessage()]);
}
