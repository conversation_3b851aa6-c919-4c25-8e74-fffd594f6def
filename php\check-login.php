<?php
/**
 * Skript pro kontrolu přihlášení
 */

// Spuštění session
session_start();

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

// Kontrola, zda je uživatel přihlášen
if (isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true) {
    echo json_encode([
        'logged_in' => true,
        'user_id' => $_SESSION['user_id'],
        'username' => $_SESSION['username'],
        'role' => $_SESSION['role']
    ]);
} else {
    echo json_encode(['logged_in' => false]);
}
