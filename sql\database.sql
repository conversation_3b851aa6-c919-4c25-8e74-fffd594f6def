-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> databáze
CREATE DATABASE IF NOT EXISTS bruntalska_sipkova_liga CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- <PERSON><PERSON><PERSON><PERSON><PERSON> databáze
USE bruntalska_sipkova_liga;

-- <PERSON>bulka pro hráče
CREATE TABLE IF NOT EXISTS players (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NULL,
    phone VARCHAR(20) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- <PERSON>bulka pro kategorie turnajů
CREATE TABLE IF NOT EXISTS tournament_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON> pro turnaje
CREATE TABLE IF NOT EXISTS tournaments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    challonge_id VARCHAR(100) NOT NULL UNIQUE,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    category_id INT NULL,
    start_date DATETIME NULL,
    end_date DATETIME NULL,
    url VARCHAR(255) NULL,
    status VARCHAR(50) NULL,
    participants_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES tournament_categories(id) ON DELETE SET NULL
);

-- Tabulka pro účastníky turnajů
CREATE TABLE IF NOT EXISTS tournament_participants (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tournament_id INT NOT NULL,
    player_id INT NULL,
    challonge_participant_id VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL,
    seed INT NULL,
    final_rank INT NULL,
    points INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tournament_id) REFERENCES tournaments(id) ON DELETE CASCADE,
    FOREIGN KEY (player_id) REFERENCES players(id) ON DELETE SET NULL,
    UNIQUE KEY unique_participant (tournament_id, challonge_participant_id)
);

-- Tabulka pro zápasy
CREATE TABLE IF NOT EXISTS matches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tournament_id INT NOT NULL,
    challonge_match_id VARCHAR(100) NOT NULL,
    round INT NOT NULL,
    player1_id INT NULL,
    player2_id INT NULL,
    score_player1 INT NULL,
    score_player2 INT NULL,
    winner_id INT NULL,
    match_date DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tournament_id) REFERENCES tournaments(id) ON DELETE CASCADE,
    FOREIGN KEY (player1_id) REFERENCES players(id) ON DELETE SET NULL,
    FOREIGN KEY (player2_id) REFERENCES players(id) ON DELETE SET NULL,
    FOREIGN KEY (winner_id) REFERENCES players(id) ON DELETE SET NULL
);

-- Tabulka pro pravidla
CREATE TABLE IF NOT EXISTS rules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    pdf_path VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabulka pro uživatele (administrátory)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    role ENUM('admin', 'editor') NOT NULL DEFAULT 'editor',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabulka pro nastavení
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Vložení výchozích kategorií turnajů
INSERT INTO tournament_categories (name, description) VALUES
('Blue Oyster Cup', 'Turnaje Blue Oyster Cup'),
('Sob Cup', 'Turnaje Sob Cup'),
('Roští Cup', 'Turnaje Roští Cup'),
('Speciální turnaje', 'Ostatní speciální turnaje');

-- Vložení výchozího nastavení
INSERT INTO settings (setting_key, setting_value) VALUES
('last_sync_time', NULL),
('challonge_api_key', 'adbPU7e6QlOzIvxzSDnohavu61YwTNhl6FIDkF3H'),
('challonge_username', 'Rezexil');
