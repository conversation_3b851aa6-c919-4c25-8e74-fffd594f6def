<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload souborů - Administrace</title>
    <link rel="stylesheet" href="css/admin.css">
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>Upload souborů pro Challonge</h1>
            <nav class="admin-nav">
                <ul>
                    <li><a href="index.html">Dashboard</a></li>
                    <li><a href="challonge.html">Challonge</a></li>
                    <li><a href="upload-files.html" class="active">Upload</a></li>
                </ul>
            </nav>
        </div>

        <div class="admin-content">
            <div id="admin-message"></div>

            <div class="admin-section">
                <h2><PERSON><PERSON><PERSON><PERSON> souborů pro Challonge</h2>
                <div class="admin-form">
                    <p>Nahrajte potřebné PHP soubory pro Challonge integraci:</p>
                    
                    <div class="form-group">
                        <label>1. update-database-for-challonge.php</label>
                        <textarea id="file1" rows="10" placeholder="Vložte obsah souboru update-database-for-challonge.php"></textarea>
                        <button onclick="uploadFile('update-database-for-challonge.php', 'file1')" class="btn btn-primary">Nahrát soubor 1</button>
                    </div>
                    
                    <div class="form-group">
                        <label>2. test-challonge-sync.php</label>
                        <textarea id="file2" rows="10" placeholder="Vložte obsah souboru test-challonge-sync.php"></textarea>
                        <button onclick="uploadFile('test-challonge-sync.php', 'file2')" class="btn btn-primary">Nahrát soubor 2</button>
                    </div>
                    
                    <div class="form-group">
                        <label>3. sync-and-evaluate-tournaments.php</label>
                        <textarea id="file3" rows="10" placeholder="Vložte obsah souboru sync-and-evaluate-tournaments.php"></textarea>
                        <button onclick="uploadFile('sync-and-evaluate-tournaments.php', 'file3')" class="btn btn-primary">Nahrát soubor 3</button>
                    </div>
                    
                    <div class="form-group">
                        <label>4. get-challonge-stats.php</label>
                        <textarea id="file4" rows="10" placeholder="Vložte obsah souboru get-challonge-stats.php"></textarea>
                        <button onclick="uploadFile('get-challonge-stats.php', 'file4')" class="btn btn-primary">Nahrát soubor 4</button>
                    </div>
                    
                    <div class="form-actions">
                        <button onclick="uploadAllFiles()" class="btn btn-success">Nahrát všechny soubory</button>
                        <button onclick="testSystem()" class="btn btn-info">Otestovat systém</button>
                    </div>
                </div>
                
                <div id="upload-results"></div>
            </div>
        </div>
    </div>

    <script>
        // Kontrola přihlášení při načtení stránky
        document.addEventListener('DOMContentLoaded', function() {
            checkLogin();
        });

        function checkLogin() {
            fetch('../php/check-login.php')
                .then(response => response.json())
                .then(data => {
                    if (!data.logged_in) {
                        window.location.href = 'login.html';
                    }
                })
                .catch(error => {
                    console.error('Chyba při kontrole přihlášení:', error);
                    window.location.href = 'login.html';
                });
        }

        function uploadFile(filename, textareaId) {
            const content = document.getElementById(textareaId).value;
            
            if (!content.trim()) {
                alert('Prosím vložte obsah souboru');
                return;
            }
            
            const formData = new FormData();
            formData.append('filename', filename);
            formData.append('content', content);
            
            fetch('../php/upload-file.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('admin-message').innerHTML = 
                        `<div class="alert alert-success">Soubor ${filename} byl úspěšně nahrán</div>`;
                } else {
                    document.getElementById('admin-message').innerHTML = 
                        `<div class="alert alert-error">Chyba při nahrávání ${filename}: ${data.message}</div>`;
                }
            })
            .catch(error => {
                console.error('Chyba při nahrávání:', error);
                document.getElementById('admin-message').innerHTML = 
                    '<div class="alert alert-error">Chyba při nahrávání souboru</div>';
            });
        }

        function uploadAllFiles() {
            const files = [
                { name: 'update-database-for-challonge.php', textareaId: 'file1' },
                { name: 'test-challonge-sync.php', textareaId: 'file2' },
                { name: 'sync-and-evaluate-tournaments.php', textareaId: 'file3' },
                { name: 'get-challonge-stats.php', textareaId: 'file4' }
            ];
            
            let uploadCount = 0;
            let successCount = 0;
            
            files.forEach(file => {
                const content = document.getElementById(file.textareaId).value;
                if (content.trim()) {
                    uploadFile(file.name, file.textareaId);
                    uploadCount++;
                }
            });
            
            if (uploadCount === 0) {
                alert('Prosím vložte obsah alespoň jednoho souboru');
            }
        }

        function testSystem() {
            document.getElementById('upload-results').innerHTML = '<p>Testování systému...</p>';
            
            fetch('../php/test-challonge-sync.php')
                .then(response => response.json())
                .then(data => {
                    let html = '<h3>Výsledky testování:</h3>';
                    
                    if (data.success) {
                        html += '<div class="alert alert-success">Všechny testy prošly úspěšně!</div>';
                        
                        if (data.tests) {
                            Object.values(data.tests).forEach(test => {
                                const statusClass = test.status === 'success' ? 'success' : 
                                                  test.status === 'warning' ? 'warning' : 'error';
                                
                                html += `
                                    <div class="test-result ${statusClass}">
                                        <h4>${test.name}</h4>
                                        <p><strong>Status:</strong> ${test.status}</p>
                                        ${test.message ? `<p>${test.message}</p>` : ''}
                                    </div>
                                `;
                            });
                        }
                    } else {
                        html += `<div class="alert alert-error">Chyba při testování: ${data.message}</div>`;
                    }
                    
                    document.getElementById('upload-results').innerHTML = html;
                })
                .catch(error => {
                    console.error('Chyba při testování:', error);
                    document.getElementById('upload-results').innerHTML = 
                        '<div class="alert alert-error">Chyba při testování systému</div>';
                });
        }
    </script>
</body>
</html>
