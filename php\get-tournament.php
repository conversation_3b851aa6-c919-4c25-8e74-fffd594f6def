<?php
/**
 * Skript pro získání detailu turnaje
 */

// Připojení k databázi
require_once 'db-connection.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

// Kontrola, zda byl zadán ID
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Chybí ID turnaje']);
    exit;
}

$tournamentId = (int) $_GET['id'];

try {
    // Získání detailu turnaje
    $query = "
        SELECT 
            t.*,
            tc.name AS category_name
        FROM 
            tournaments t
        LEFT JOIN 
            tournament_categories tc ON t.category_id = tc.id
        WHERE 
            t.id = ?
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute([$tournamentId]);
    $tournament = $stmt->fetch();
    
    if (!$tournament) {
        http_response_code(404);
        echo json_encode(['error' => 'Turnaj nebyl nalezen']);
        exit;
    }
    
    // Vrácení detailu turnaje jako JSON
    echo json_encode($tournament);
} catch (PDOException $e) {
    // Vrácení chybové zprávy jako JSON
    http_response_code(500);
    echo json_encode(['error' => 'Nepodařilo se získat detail turnaje: ' . $e->getMessage()]);
}
