<?php
/**
 * Skript pro získání seznamu turnajů
 */

// Připojení k <PERSON>b<PERSON>zi
require_once 'db-connection.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

try {
    // Získání seznamu turnajů
    $query = "
        SELECT 
            t.*,
            tc.name AS category_name
        FROM 
            tournaments t
        LEFT JOIN 
            tournament_categories tc ON t.category_id = tc.id
        ORDER BY 
            t.start_date DESC
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $tournaments = $stmt->fetchAll();
    
    // Vrácení seznamu turnajů jako JSON
    echo json_encode($tournaments);
} catch (PDOException $e) {
    // Vrácení chybové zprávy jako JSON
    http_response_code(500);
    echo json_encode(['error' => 'Nepodařilo se získat seznam turnajů: ' . $e->getMessage()]);
}
