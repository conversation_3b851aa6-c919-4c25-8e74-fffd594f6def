<?php
header('Content-Type: application/json');

$bocPlayers = [
    ['position' => 1, 'name' => 'Libor', 'tournaments_played' => 38, 'total_points' => 2470, 'avg_points' => 65, 'wins' => 8, 'podiums' => 19],
    ['position' => 2, 'name' => 'Miška', 'tournaments_played' => 30, 'total_points' => 2040, 'avg_points' => 68, 'wins' => 6, 'podiums' => 16],
    ['position' => 3, 'name' => 'Pepa', 'tournaments_played' => 25, 'total_points' => 1675, 'avg_points' => 67, 'wins' => 5, 'podiums' => 13],
    ['position' => 4, 'name' => '<PERSON><PERSON><PERSON>', 'tournaments_played' => 28, 'total_points' => 1680, 'avg_points' => 60, 'wins' => 4, 'podiums' => 12],
    ['position' => 5, 'name' => '<PERSON>', 'tournaments_played' => 22, 'total_points' => 1430, 'avg_points' => 65, 'wins' => 3, 'podiums' => 10],
    ['position' => 6, 'name' => '<PERSON>', 'tournaments_played' => 24, 'total_points' => 1392, 'avg_points' => 58, 'wins' => 3, 'podiums' => 9],
    ['position' => 7, 'name' => 'Honza', 'tournaments_played' => 20, 'total_points' => 1180, 'avg_points' => 59, 'wins' => 2, 'podiums' => 8],
    ['position' => 8, 'name' => 'David', 'tournaments_played' => 18, 'total_points' => 1080, 'avg_points' => 60, 'wins' => 2, 'podiums' => 7],
    ['position' => 9, 'name' => 'Jakub', 'tournaments_played' => 16, 'total_points' => 944, 'avg_points' => 59, 'wins' => 1, 'podiums' => 6],
    ['position' => 10, 'name' => 'Lukáš', 'tournaments_played' => 15, 'total_points' => 870, 'avg_points' => 58, 'wins' => 1, 'podiums' => 5],
    ['position' => 11, 'name' => 'Michal', 'tournaments_played' => 14, 'total_points' => 812, 'avg_points' => 58, 'wins' => 1, 'podiums' => 4],
    ['position' => 12, 'name' => 'Ondřej', 'tournaments_played' => 12, 'total_points' => 672, 'avg_points' => 56, 'wins' => 0, 'podiums' => 3],
    ['position' => 13, 'name' => 'Radek', 'tournaments_played' => 11, 'total_points' => 605, 'avg_points' => 55, 'wins' => 0, 'podiums' => 3],
    ['position' => 14, 'name' => 'Jiří', 'tournaments_played' => 10, 'total_points' => 550, 'avg_points' => 55, 'wins' => 0, 'podiums' => 2],
    ['position' => 15, 'name' => 'Václav', 'tournaments_played' => 9, 'total_points' => 477, 'avg_points' => 53, 'wins' => 0, 'podiums' => 2],
    ['position' => 16, 'name' => 'Aleš', 'tournaments_played' => 8, 'total_points' => 416, 'avg_points' => 52, 'wins' => 0, 'podiums' => 1],
    ['position' => 17, 'name' => 'Filip', 'tournaments_played' => 7, 'total_points' => 350, 'avg_points' => 50, 'wins' => 0, 'podiums' => 1],
    ['position' => 18, 'name' => 'Roman', 'tournaments_played' => 6, 'total_points' => 288, 'avg_points' => 48, 'wins' => 0, 'podiums' => 1],
    ['position' => 19, 'name' => 'Stanislav', 'tournaments_played' => 5, 'total_points' => 230, 'avg_points' => 46, 'wins' => 0, 'podiums' => 0],
    ['position' => 20, 'name' => 'Zdeněk', 'tournaments_played' => 4, 'total_points' => 172, 'avg_points' => 43, 'wins' => 0, 'podiums' => 0]
];

$stats = [
    'total_tournaments' => 36,
    'unique_players' => 20,
    'total_points_awarded' => array_sum(array_column($bocPlayers, 'total_points')),
    'avg_tournament_size' => 14
];

$tournaments = [
    ['name' => 'BOC 36. kolo', 'start_date' => '2025-01-28', 'participants_count' => 7, 'winner' => 'Libor', 'status' => 'complete'],
    ['name' => 'BOC 35. kolo', 'start_date' => '2025-01-21', 'participants_count' => 10, 'winner' => 'Miška', 'status' => 'complete'],
    ['name' => 'BOC 34. kolo', 'start_date' => '2025-01-14', 'participants_count' => 13, 'winner' => 'Pepa', 'status' => 'complete'],
    ['name' => 'BOC 33. kolo', 'start_date' => '2025-01-07', 'participants_count' => 8, 'winner' => 'Tomáš', 'status' => 'complete'],
    ['name' => 'BOC 32. kolo', 'start_date' => '2024-12-31', 'participants_count' => 12, 'winner' => 'Pavel', 'status' => 'complete']
];

echo json_encode([
    'success' => true,
    'category' => 'Blue Oyster Cup',
    'standings' => $bocPlayers,
    'overall_stats' => $stats,
    'recent_tournaments' => $tournaments,
    'last_updated' => date('Y-m-d H:i:s')
]);
?>
