<?php
/**
 * Skript pro získání tabulky podle kategorie turnajů s body a účastmi
 */

// Připojení k <PERSON>bázi
require_once 'db-connection.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

try {
    // Získání parametrů
    $category = $_GET['category'] ?? 'Blue Oyster Cup';
    $season = $_GET['season'] ?? '2024-25';
    $limit = (int) ($_GET['limit'] ?? 100);
    
    // Získání ID kategorie
    $stmt = $pdo->prepare("SELECT id, name FROM tournament_categories WHERE name = ?");
    $stmt->execute([$category]);
    $categoryData = $stmt->fetch();
    
    if (!$categoryData) {
        echo json_encode([
            'success' => false,
            'message' => "Kategorie '{$category}' nebyla nalezena",
            'standings' => []
        ]);
        exit;
    }
    
    $categoryId = $categoryData['id'];
    $categoryName = $categoryData['name'];
    
    // Hlavní dotaz pro tabulku podle kategorie
    $query = "
        SELECT 
            tp.name,
            COUNT(tp.id) as tournaments_played,
            SUM(tp.points) as total_points,
            AVG(tp.points) as avg_points,
            MIN(tp.final_rank) as best_rank,
            MAX(tp.final_rank) as worst_rank,
            COUNT(CASE WHEN tp.final_rank = 1 THEN 1 END) as wins,
            COUNT(CASE WHEN tp.final_rank <= 3 THEN 1 END) as podiums,
            COUNT(CASE WHEN tp.final_rank <= 8 THEN 1 END) as top8,
            GROUP_CONCAT(
                CONCAT(t.name, ':', tp.final_rank, ':', tp.points, ':', DATE(t.start_date)) 
                ORDER BY t.start_date DESC 
                SEPARATOR '|'
            ) as tournament_details,
            MAX(t.start_date) as last_tournament_date
        FROM tournament_participants tp
        JOIN tournaments t ON tp.tournament_id = t.id
        WHERE t.category_id = ?
        AND t.status = 'complete'
        GROUP BY tp.name
        HAVING tournaments_played > 0
        ORDER BY total_points DESC, tournaments_played DESC, avg_points DESC, best_rank ASC
        LIMIT ?
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute([$categoryId, $limit]);
    $standings = $stmt->fetchAll();
    
    // Zpracování výsledků
    $processedStandings = [];
    $position = 1;
    
    foreach ($standings as $player) {
        // Zpracování detailů turnajů
        $tournamentHistory = [];
        if ($player['tournament_details']) {
            $details = explode('|', $player['tournament_details']);
            foreach ($details as $detail) {
                $parts = explode(':', $detail);
                if (count($parts) >= 4) {
                    $tournamentHistory[] = [
                        'tournament_name' => $parts[0],
                        'final_rank' => (int)$parts[1],
                        'points' => (int)$parts[2],
                        'date' => $parts[3]
                    ];
                }
            }
        }
        
        // Výpočet úspěšnosti
        $winRate = $player['tournaments_played'] > 0 
            ? round(($player['wins'] / $player['tournaments_played']) * 100, 1)
            : 0;
            
        $podiumRate = $player['tournaments_played'] > 0 
            ? round(($player['podiums'] / $player['tournaments_played']) * 100, 1)
            : 0;
            
        $top8Rate = $player['tournaments_played'] > 0 
            ? round(($player['top8'] / $player['tournaments_played']) * 100, 1)
            : 0;
        
        // Určení trendu (posledních 5 turnajů)
        $recentTournaments = array_slice($tournamentHistory, 0, 5);
        $recentPoints = array_sum(array_column($recentTournaments, 'points'));
        $recentAvg = count($recentTournaments) > 0 ? round($recentPoints / count($recentTournaments), 1) : 0;
        
        $processedStandings[] = [
            'position' => $position,
            'name' => $player['name'],
            'tournaments_played' => (int)$player['tournaments_played'],
            'total_points' => (int)$player['total_points'],
            'avg_points' => round($player['avg_points'], 1),
            'recent_avg_points' => $recentAvg,
            'best_rank' => (int)$player['best_rank'],
            'worst_rank' => (int)$player['worst_rank'],
            'wins' => (int)$player['wins'],
            'podiums' => (int)$player['podiums'],
            'top8' => (int)$player['top8'],
            'win_rate' => $winRate,
            'podium_rate' => $podiumRate,
            'top8_rate' => $top8Rate,
            'last_tournament_date' => $player['last_tournament_date'],
            'tournament_history' => $tournamentHistory
        ];
        
        $position++;
    }
    
    // Získání dalších statistik pro kategorii
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(DISTINCT t.id) as total_tournaments,
            COUNT(DISTINCT tp.name) as unique_players,
            SUM(tp.points) as total_points_awarded,
            AVG(t.participants_count) as avg_tournament_size,
            MAX(t.participants_count) as max_tournament_size,
            MIN(t.participants_count) as min_tournament_size
        FROM tournaments t
        LEFT JOIN tournament_participants tp ON t.id = tp.tournament_id
        WHERE t.category_id = ? AND t.status = 'complete'
    ");
    $stmt->execute([$categoryId]);
    $overallStats = $stmt->fetch();
    
    // Nejnovější turnaje
    $stmt = $pdo->prepare("
        SELECT 
            t.name,
            t.start_date,
            t.participants_count,
            t.status,
            (SELECT tp2.name FROM tournament_participants tp2 WHERE tp2.tournament_id = t.id AND tp2.final_rank = 1 LIMIT 1) as winner
        FROM tournaments t
        WHERE t.category_id = ?
        ORDER BY t.start_date DESC
        LIMIT 10
    ");
    $stmt->execute([$categoryId]);
    $recentTournaments = $stmt->fetchAll();
    
    // Statistiky podle měsíců (posledních 12 měsíců)
    $stmt = $pdo->prepare("
        SELECT 
            DATE_FORMAT(t.start_date, '%Y-%m') as month,
            COUNT(DISTINCT t.id) as tournaments_count,
            COUNT(DISTINCT tp.name) as players_count,
            AVG(t.participants_count) as avg_participants
        FROM tournaments t
        LEFT JOIN tournament_participants tp ON t.id = tp.tournament_id
        WHERE t.category_id = ? 
        AND t.status = 'complete'
        AND t.start_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(t.start_date, '%Y-%m')
        ORDER BY month DESC
    ");
    $stmt->execute([$categoryId]);
    $monthlyStats = $stmt->fetchAll();
    
    // Vrácení výsledků
    echo json_encode([
        'success' => true,
        'category' => $categoryName,
        'category_id' => $categoryId,
        'season' => $season,
        'standings' => $processedStandings,
        'overall_stats' => $overallStats,
        'recent_tournaments' => $recentTournaments,
        'monthly_stats' => $monthlyStats,
        'last_updated' => date('Y-m-d H:i:s')
    ]);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Chyba při získávání tabulky: ' . $e->getMessage(),
        'standings' => []
    ]);
}
?>
