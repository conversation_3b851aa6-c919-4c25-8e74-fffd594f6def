<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sob Cup - Bruntálská šipková liga</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* Dropdown menu styling */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-menu {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background-color: #dc143c;
            min-width: 180px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
            border-radius: 5px;
            margin-top: 5px;
        }

        .dropdown-menu li {
            margin: 0;
        }

        .dropdown-menu a {
            color: #ffffff;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            border-radius: 0;
        }

        .dropdown-menu a:hover {
            background-color: #06cef9;
        }

        .dropdown:hover .dropdown-menu {
            display: block;
        }

        @media (max-width: 768px) {
            .dropdown-menu {
                position: static;
                display: block;
                box-shadow: none;
                background-color: transparent;
                margin-top: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Hlavička -->
    <header>
        <div class="container">
            <h1>Bruntálská šipková liga</h1>
            <nav>
                <ul>
                    <li><a href="index.html">Domů</a></li>
                    <li><a href="hraci.html">Hráči</a></li>
                    <li class="dropdown">
                        <a href="liga.html">Liga</a>
                        <ul class="dropdown-menu">
                            <li><a href="liga.html#tabulka">Tabulka ligy</a></li>
                            <li><a href="liga.html#rozpis">Rozpis zápasů</a></li>
                            <li><a href="liga.html#pravidla">Pravidla</a></li>
                        </ul>
                    </li>
                    <li><a href="blue-oyster-cup.html">Blue Oyster Cup</a></li>
                    <li><a href="rosti-cup.html">Roští Cup</a></li>
                    <li><a href="sob-cup.html" class="active">Sob Cup</a></li>
                    <li><a href="specialni.html">Speciální turnaje</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Obsah -->
    <main>
        <div class="container">
            <!-- Informace o turnaji -->
            <section>
                <h2>Sob Cup</h2>
                <p>Turnaje série Sob Cup se konají každý čtvrtek od října po dobu ~30 týdnů s vánoční přestávkou.</p>
            </section>

            <!-- Tabulka Sob Cup -->
            <section>
                <h2>Tabulka Sob Cup</h2>
                <p>Celková tabulka hráčů s body a počtem účastí:</p>
                <div style="margin: 20px 0;">
                    <a href="tabulky-turnaju.html" class="btn" style="background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 1.1em;">📊 Zobrazit tabulku Sob Cup</a>
                </div>
            </section>

            <!-- Seznam turnajů -->
            <section>
                <h2>Seznam turnajů Sob Cup</h2>
                <div id="sob-tournaments">
                    <p>Načítání turnajů...</p>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>&copy; 2025 Bruntálská šipková liga</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadSobTournaments();
        });

        function loadSobTournaments() {
            const container = document.getElementById('sob-tournaments');

            fetch('php/get-category-standings.php?category=Sob Cup&limit=0')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.recent_tournaments) {
                        displayTournaments(data.recent_tournaments);
                    } else {
                        container.innerHTML = '<p>Žádné turnaje k zobrazení nebo data nejsou synchronizována.</p>';
                    }
                })
                .catch(error => {
                    console.error('Chyba při načítání turnajů:', error);
                    container.innerHTML = '<p>Chyba při načítání turnajů. Zkuste to později.</p>';
                });
        }

        function displayTournaments(tournaments) {
            const container = document.getElementById('sob-tournaments');

            if (!tournaments || tournaments.length === 0) {
                container.innerHTML = '<p>Žádné turnaje k zobrazení</p>';
                return;
            }

            let html = `
                <table>
                    <thead>
                        <tr>
                            <th>Název</th>
                            <th>Datum</th>
                            <th>Účastníci</th>
                            <th>Vítěz</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            tournaments.forEach(tournament => {
                const date = tournament.start_date
                    ? new Date(tournament.start_date).toLocaleDateString('cs-CZ')
                    : 'N/A';

                const status = getStatusText(tournament.status);

                html += `
                    <tr>
                        <td>${tournament.name}</td>
                        <td>${date}</td>
                        <td>${tournament.participants_count || 0}</td>
                        <td>${tournament.winner || 'N/A'}</td>
                        <td>${status}</td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                </table>
            `;

            container.innerHTML = html;
        }

        function getStatusText(status) {
            switch (status) {
                case 'pending':
                    return 'Čeká na zahájení';
                case 'underway':
                    return 'Probíhá';
                case 'complete':
                    return 'Dokončeno';
                default:
                    return 'Neurčeno';
            }
        }
    </script>
</body>
</html>
