document.addEventListener('DOMContentLoaded', function() {
    // Načtení seznamu hráč<PERSON>
    if (document.getElementById('players-list')) {
        fetchPlayers();
    }
});

/**
 * Načte seznam hráčů z API
 */
function fetchPlayers() {
    const playersContainer = document.getElementById('players-list');
    
    fetch('php/get-players-stats.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se načíst seznam hráčů');
            }
            return response.json();
        })
        .then(data => {
            if (data.length === 0) {
                playersContainer.innerHTML = '<p>Žádn<PERSON> hráči k zobrazení</p>';
                return;
            }

            // Vytvoření tabulky s hráči
            let html = `
                <table>
                    <thead>
                        <tr>
                            <th>J<PERSON><PERSON></th>
                            <th><PERSON><PERSON><PERSON><PERSON><PERSON> turn<PERSON></th>
                            <th>V<PERSON>hry</th>
                            <th><PERSON>hry</th>
                            <th>Úspěšnost</th>
                            <th><PERSON><PERSON>ce</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            data.forEach(player => {
                const winRate = player.matches > 0 
                    ? Math.round((player.wins / player.matches) * 100) 
                    : 0;
                
                html += `
                    <tr>
                        <td>${player.name}</td>
                        <td>${player.tournaments}</td>
                        <td>${player.wins}</td>
                        <td>${player.losses}</td>
                        <td>${winRate}%</td>
                        <td><a href="hrac.html?id=${player.id}" class="btn">Detail</a></td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                </table>
            `;

            playersContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba při načítání seznamu hráčů:', error);
            playersContainer.innerHTML = `<p>Nepodařilo se načíst seznam hráčů: ${error.message}</p>`;
        });
}
