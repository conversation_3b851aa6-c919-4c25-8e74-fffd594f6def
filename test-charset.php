<?php
header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test českých znaků</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Test zobrazení česk<PERSON>ch znak<PERSON></h1>
    
    <div class="test-section">
        <h2><PERSON><PERSON><PERSON><PERSON><PERSON> čes<PERSON>na<PERSON></h2>
        <p>áčďéěíňóřšťúůýž ÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ</p>
    </div>
    
    <div class="test-section">
        <h2>Šipkové termíny</h2>
        <p>Bruntálská šipková liga</p>
        <p>Nadcházející turnaje</p>
        <p>Výsledky a statistiky</p>
        <p>Pravidla hry</p>
    </div>
    
    <div class="test-section">
        <h2>Názvy turnajů</h2>
        <p>Blue Oyster Cup</p>
        <p>Roští Cup</p>
        <p>Sob Cup</p>
        <p>Vánoční turnaj</p>
        <p>Novoroční cup</p>
    </div>
    
    <div class="test-section">
        <h2>JavaScript test</h2>
        <p id="js-test">Načítání...</p>
    </div>
    
    <script>
        document.getElementById('js-test').textContent = 'České znaky z JavaScriptu: áčďéěíňóřšťúůýž';
    </script>
</body>
</html>
