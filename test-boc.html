<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test BOC - Bruntálská šipková liga</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: center; }
        th { background: #dc143c; color: white; }
        .stats { display: flex; gap: 20px; margin: 20px 0; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; }
        .stat-value { font-size: 1.5rem; font-weight: bold; color: #dc143c; }
    </style>
</head>
<body>
    <h1>Blue Oyster Cup - Test</h1>
    
    <div id="boc-standings">
        <p><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tabul<PERSON>...</p>
    </div>
    
    <div id="boc-tournaments">
        <p><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> turn<PERSON>...</p>
    </div>

    <script>
        console.log('Test JavaScript se spustil!');
        
        // Statická data pro BOC
        const bocPlayers = [
            {position: 1, name: 'Libor', tournaments_played: 38, total_points: 2470, avg_points: 65, wins: 8, podiums: 19},
            {position: 2, name: 'Miška', tournaments_played: 30, total_points: 2040, avg_points: 68, wins: 6, podiums: 16},
            {position: 3, name: 'Pepa', tournaments_played: 25, total_points: 1675, avg_points: 67, wins: 5, podiums: 13},
            {position: 4, name: 'Tomáš', tournaments_played: 28, total_points: 1680, avg_points: 60, wins: 4, podiums: 12},
            {position: 5, name: 'Pavel', tournaments_played: 22, total_points: 1430, avg_points: 65, wins: 3, podiums: 10}
        ];

        const stats = {
            total_tournaments: 36,
            unique_players: 20,
            total_points_awarded: 25000,
            avg_tournament_size: 14
        };

        const tournaments = [
            {name: 'BOC 36. kolo', start_date: '2025-01-28', participants_count: 7, winner: 'Libor', status: 'complete'},
            {name: 'BOC 35. kolo', start_date: '2025-01-21', participants_count: 10, winner: 'Miška', status: 'complete'},
            {name: 'BOC 34. kolo', start_date: '2025-01-14', participants_count: 13, winner: 'Pepa', status: 'complete'}
        ];

        function displayStandings() {
            const container = document.getElementById('boc-standings');
            
            let html = '<h2>Tabulka Blue Oyster Cup</h2>';
            
            // Statistiky
            html += `
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-value">${stats.total_tournaments}</div>
                        <div>Turnajů</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${stats.unique_players}</div>
                        <div>Hráčů</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${stats.total_points_awarded}</div>
                        <div>Bodů</div>
                    </div>
                </div>
            `;

            // Tabulka
            html += `
                <table>
                    <thead>
                        <tr>
                            <th>Pořadí</th>
                            <th>Jméno</th>
                            <th>Účasti</th>
                            <th>Body</th>
                            <th>Průměr</th>
                            <th>Výhry</th>
                            <th>Pódium</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            bocPlayers.forEach(player => {
                let medal = '';
                if (player.position === 1) medal = '🥇 ';
                else if (player.position === 2) medal = '🥈 ';
                else if (player.position === 3) medal = '🥉 ';
                
                html += `
                    <tr>
                        <td>${medal}${player.position}.</td>
                        <td style="font-weight: bold;">${player.name}</td>
                        <td>${player.tournaments_played}</td>
                        <td style="font-weight: bold; color: #007bff;">${player.total_points}</td>
                        <td>${player.avg_points}</td>
                        <td>${player.wins}</td>
                        <td>${player.podiums}</td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        function displayTournaments() {
            const container = document.getElementById('boc-tournaments');
            
            let html = '<h2>Nejnovější turnaje</h2>';
            html += `
                <table>
                    <thead>
                        <tr>
                            <th>Název</th>
                            <th>Datum</th>
                            <th>Účastníci</th>
                            <th>Vítěz</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            tournaments.forEach(tournament => {
                const date = new Date(tournament.start_date).toLocaleDateString('cs-CZ');
                html += `
                    <tr>
                        <td>${tournament.name}</td>
                        <td>${date}</td>
                        <td>${tournament.participants_count}</td>
                        <td>${tournament.winner}</td>
                        <td>Dokončeno</td>
                    </tr>
                `;
            });

            html += '</tbody></table>';
            container.innerHTML = html;
        }

        // Spustíme po načtení stránky
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOMContentLoaded event fired!');
            displayStandings();
            displayTournaments();
        });

        // Backup - spustíme i bez DOMContentLoaded
        setTimeout(function() {
            console.log('Timeout backup spuštěn');
            if (document.getElementById('boc-standings').innerHTML.includes('Načítání')) {
                displayStandings();
                displayTournaments();
            }
        }, 1000);
    </script>
</body>
</html>
