document.addEventListener('DOMContentLoaded', function() {
    // Načtení pravidel
    if (document.getElementById('rules-content')) {
        fetchRules();
    }
    
    // Načtení PDF dokumentů
    if (document.getElementById('pdf-list')) {
        fetchPdfList();
    }
});

/**
 * Načte pravidla z API
 */
function fetchRules() {
    const rulesContainer = document.getElementById('rules-content');
    
    fetch('php/get-rules.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se načíst pravidla');
            }
            return response.json();
        })
        .then(data => {
            if (!data || data.length === 0) {
                // Pokud nejsou pravidla v databázi, zobrazíme výchozí text
                rulesContainer.innerHTML = `
                    <h3>Základní pravidla Bruntálské šipkov<PERSON> ligy</h3>
                    <p>Pravidla budou brzy k dispozici. Zatím můžete stáhnout PDF dokumenty níže.</p>
                `;
                return;
            }

            // Zobrazení pravidel
            let html = '';
            
            data.forEach(rule => {
                html += `
                    <div class="rule-section">
                        <h3>${rule.title}</h3>
                        <div class="rule-content">
                            ${rule.content}
                        </div>
                    </div>
                `;
            });

            rulesContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba při načítání pravidel:', error);
            rulesContainer.innerHTML = `
                <h3>Základní pravidla Bruntálské šipkové ligy</h3>
                <p>Pravidla budou brzy k dispozici. Zatím můžete stáhnout PDF dokumenty níže.</p>
                <p class="error">Chyba při načítání pravidel: ${error.message}</p>
            `;
        });
}

/**
 * Načte seznam PDF dokumentů s pravidly
 */
function fetchPdfList() {
    const pdfListContainer = document.getElementById('pdf-list');
    
    fetch('php/get-rules.php?format=pdf')
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se načíst PDF dokumenty');
            }
            return response.json();
        })
        .then(data => {
            if (!data || data.length === 0) {
                pdfListContainer.innerHTML = '<p>Žádné PDF dokumenty k dispozici</p>';
                return;
            }

            // Zobrazení seznamu PDF dokumentů
            let html = '<ul class="pdf-documents">';
            
            data.forEach(pdf => {
                html += `
                    <li>
                        <a href="php/view-rules-pdf.php?id=${pdf.id}" target="_blank">
                            ${pdf.title}
                        </a>
                    </li>
                `;
            });

            html += '</ul>';
            pdfListContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba při načítání PDF dokumentů:', error);
            pdfListContainer.innerHTML = `<p>Nepodařilo se načíst PDF dokumenty: ${error.message}</p>`;
        });
}
