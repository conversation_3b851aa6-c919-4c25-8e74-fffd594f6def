# Nastavení kódování pro české znaky
AddDefaultCharset UTF-8
AddCharset UTF-8 .html
AddCharset UTF-8 .css
AddCharset UTF-8 .js
AddCharset UTF-8 .php

# Nastavení Content-Type hlaviček
<FilesMatch "\.(html|htm|php)$">
    Header set Content-Type "text/html; charset=UTF-8"
</FilesMatch>

<FilesMatch "\.(css)$">
    Header set Content-Type "text/css; charset=UTF-8"
</FilesMatch>

<FilesMatch "\.(js)$">
    Header set Content-Type "application/javascript; charset=UTF-8"
</FilesMatch>

# Povolení mod_rewrite
RewriteEngine On

# Přesměrování index.html na index.php
RewriteRule ^index\.html$ index.php [R=301,L]

# Nastavení index.php jako výchozí stránka
DirectoryIndex index.php index.html

# Bezpečnostní hlavi<PERSON><PERSON>
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
