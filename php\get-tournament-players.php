<?php
/**
 * Skript pro získ<PERSON><PERSON> hrá<PERSON><PERSON> turnaje nebo turnajů hráče
 */

// Připojení k <PERSON>bázi
require_once 'db-connection.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

try {
    // Pokud je zadáno ID turnaje, získ<PERSON>me hráče tohoto turnaje
    if (isset($_GET['tournament_id']) && is_numeric($_GET['tournament_id'])) {
        $tournamentId = (int) $_GET['tournament_id'];
        
        $query = "
            SELECT 
                p.*,
                tp.seed,
                tp.final_rank
            FROM 
                tournament_participants tp
            JOIN 
                players p ON tp.player_id = p.id
            WHERE 
                tp.tournament_id = ?
            ORDER BY 
                tp.final_rank IS NULL, tp.final_rank, tp.seed, p.name
        ";
        
        $stmt = $pdo->prepare($query);
        $stmt->execute([$tournamentId]);
        $players = $stmt->fetchAll();
        
        echo json_encode($players);
    }
    // Pokud je zadáno ID hráče, získáme turnaje tohoto hráče
    elseif (isset($_GET['player_id']) && is_numeric($_GET['player_id'])) {
        $playerId = (int) $_GET['player_id'];
        
        $query = "
            SELECT 
                t.*,
                tp.seed,
                tp.final_rank,
                tc.name AS category_name
            FROM 
                tournament_participants tp
            JOIN 
                tournaments t ON tp.tournament_id = t.id
            LEFT JOIN 
                tournament_categories tc ON t.category_id = tc.id
            WHERE 
                tp.player_id = ?
            ORDER BY 
                t.start_date DESC, t.name
        ";
        
        $stmt = $pdo->prepare($query);
        $stmt->execute([$playerId]);
        $tournaments = $stmt->fetchAll();
        
        echo json_encode($tournaments);
    }
    else {
        http_response_code(400);
        echo json_encode(['error' => 'Chybí ID turnaje nebo hráče']);
    }
} catch (PDOException $e) {
    // Vrácení chybové zprávy jako JSON
    http_response_code(500);
    echo json_encode(['error' => 'Nepodařilo se získat data: ' . $e->getMessage()]);
}
