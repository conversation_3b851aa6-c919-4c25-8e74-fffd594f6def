<?php
/**
 * Skript pro aktualizaci nastavení Challonge API
 */

// Spuštění session
session_start();

// Kontrola, zda je uživatel přihlášen
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('HTTP/1.1 401 Unauthorized');
    echo json_encode(['success' => false, 'message' => 'Nejste přihlášen']);
    exit;
}

// Kontrola, zda byl odeslán formulář
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('HTTP/1.1 405 Method Not Allowed');
    echo json_encode(['success' => false, 'message' => 'Neplatný požadavek']);
    exit;
}

// Získání nastavení
$apiKey = isset($_POST['api_key']) ? trim($_POST['api_key']) : '';
$username = isset($_POST['username']) ? trim($_POST['username']) : '';

// Připojení k databázi
require_once 'db-connection.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

try {
    // Aktualizace API klíče
    $stmt = $pdo->prepare("
        INSERT INTO settings (setting_key, setting_value, created_at, updated_at)
        VALUES ('challonge_api_key', ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = NOW()
    ");
    $stmt->execute([$apiKey, $apiKey]);
    
    // Aktualizace uživatelského jména
    $stmt = $pdo->prepare("
        INSERT INTO settings (setting_key, setting_value, created_at, updated_at)
        VALUES ('challonge_username', ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = NOW()
    ");
    $stmt->execute([$username, $username]);
    
    // Vrácení úspěšné odpovědi
    echo json_encode(['success' => true, 'message' => 'Nastavení bylo úspěšně aktualizováno']);
} catch (PDOException $e) {
    // Vrácení chybové zprávy jako JSON
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Nepodařilo se aktualizovat nastavení: ' . $e->getMessage()]);
}
