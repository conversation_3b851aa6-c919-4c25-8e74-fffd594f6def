document.addEventListener('DOMContentLoaded', function() {
    // Načtení tabulky ligy
    fetchLeagueStandings();
    
    // Načtení turnajů ligy
    fetchLeagueTournaments();
    
    // Načtení statistik ligy
    fetchLeagueStats();
    
    // Obsluha přepínání záložek
    setupTabs();
});

/**
 * Nastaví přepínání z<PERSON>
 */
function setupTabs() {
    const tabLinks = document.querySelectorAll('.tabs a');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Odstranění aktivní třídy ze všech záložek
            tabLinks.forEach(l => l.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // Přidání aktivní třídy na kliknutou záložku
            this.classList.add('active');
            
            // Zobrazení odpovídajícího obsahu
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });
}

/**
 * Načte tabulku ligy z API
 */
function fetchLeagueStandings() {
    const standingsContainer = document.getElementById('standings-content');
    
    fetch('php/get-players-stats.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se načíst tabulku ligy');
            }
            return response.json();
        })
        .then(data => {
            if (!data || data.length === 0) {
                standingsContainer.innerHTML = '<p>Žádná data k zobrazení</p>';
                return;
            }

            // Seřazení hráčů podle počtu výher a odehraných turnajů
            data.sort((a, b) => {
                if (b.wins !== a.wins) {
                    return b.wins - a.wins;
                }
                if (b.tournaments !== a.tournaments) {
                    return b.tournaments - a.tournaments;
                }
                return a.name.localeCompare(b.name);
            });
            
            // Zobrazení tabulky ligy
            let html = `
                <table>
                    <thead>
                        <tr>
                            <th>Pořadí</th>
                            <th>Jméno</th>
                            <th>Odehrané turnaje</th>
                            <th>Výhry</th>
                            <th>Prohry</th>
                            <th>Úspěšnost</th>
                            <th>Akce</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            data.forEach((player, index) => {
                const winRate = player.matches > 0 
                    ? Math.round((player.wins / player.matches) * 100) 
                    : 0;
                
                html += `
                    <tr>
                        <td>${index + 1}.</td>
                        <td>${player.name}</td>
                        <td>${player.tournaments}</td>
                        <td>${player.wins}</td>
                        <td>${player.losses}</td>
                        <td>${winRate}%</td>
                        <td><a href="hrac.html?id=${player.id}" class="btn">Detail</a></td>
                    </tr>
                `;
            });
            
            html += `
                    </tbody>
                </table>
            `;
            
            standingsContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba při načítání tabulky ligy:', error);
            standingsContainer.innerHTML = `<p>Nepodařilo se načíst tabulku ligy: ${error.message}</p>`;
        });
}

/**
 * Načte turnaje ligy z API
 */
function fetchLeagueTournaments() {
    const tournamentsContainer = document.getElementById('league-tournaments-content');
    
    fetch('php/get-tournaments.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se načíst turnaje ligy');
            }
            return response.json();
        })
        .then(data => {
            if (!data || data.length === 0) {
                tournamentsContainer.innerHTML = '<p>Žádné turnaje k zobrazení</p>';
                return;
            }

            // Seskupení turnajů podle kategorií
            const categories = {};
            data.forEach(tournament => {
                const categoryName = tournament.category_name || 'Nezařazeno';
                if (!categories[categoryName]) {
                    categories[categoryName] = [];
                }
                categories[categoryName].push(tournament);
            });
            
            // Zobrazení turnajů ligy podle kategorií
            let html = '';
            
            Object.keys(categories).forEach(category => {
                html += `
                    <div class="tournament-category">
                        <h4>${category}</h4>
                        <table>
                            <thead>
                                <tr>
                                    <th>Název</th>
                                    <th>Datum</th>
                                    <th>Status</th>
                                    <th>Akce</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                // Seřazení turnajů podle data (nejnovější první)
                categories[category].sort((a, b) => new Date(b.start_date) - new Date(a.start_date));
                
                categories[category].forEach(tournament => {
                    const startDate = tournament.start_date 
                        ? new Date(tournament.start_date).toLocaleDateString('cs-CZ') 
                        : 'Neurčeno';
                    const status = getStatusText(tournament.status);
                    
                    html += `
                        <tr>
                            <td>${tournament.name}</td>
                            <td>${startDate}</td>
                            <td>${status}</td>
                            <td><a href="turnaj.html?id=${tournament.id}" class="btn">Detail</a></td>
                        </tr>
                    `;
                });
                
                html += `
                            </tbody>
                        </table>
                    </div>
                `;
            });
            
            tournamentsContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba při načítání turnajů ligy:', error);
            tournamentsContainer.innerHTML = `<p>Nepodařilo se načíst turnaje ligy: ${error.message}</p>`;
        });
}

/**
 * Načte statistiky ligy z API
 */
function fetchLeagueStats() {
    const statsContainer = document.getElementById('league-stats-content');
    
    fetch('php/get-players-stats.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se načíst statistiky ligy');
            }
            return response.json();
        })
        .then(data => {
            if (!data || data.length === 0) {
                statsContainer.innerHTML = '<p>Žádná data k zobrazení</p>';
                return;
            }

            // Výpočet celkových statistik ligy
            const totalStats = {
                players: data.length,
                tournaments: 0,
                matches: 0,
                wins: 0,
                topPlayer: { name: '', wins: 0 },
                mostActive: { name: '', tournaments: 0 }
            };
            
            // Nalezení hráče s nejvíce výhrami a nejvíce odehranými turnaji
            data.forEach(player => {
                totalStats.tournaments += player.tournaments;
                totalStats.matches += player.matches;
                totalStats.wins += player.wins;
                
                if (player.wins > totalStats.topPlayer.wins) {
                    totalStats.topPlayer = { name: player.name, wins: player.wins };
                }
                
                if (player.tournaments > totalStats.mostActive.tournaments) {
                    totalStats.mostActive = { name: player.name, tournaments: player.tournaments };
                }
            });
            
            // Průměrný počet turnajů na hráče
            const avgTournaments = totalStats.players > 0 
                ? Math.round((totalStats.tournaments / totalStats.players) * 10) / 10 
                : 0;
            
            // Průměrný počet zápasů na turnaj
            const avgMatches = totalStats.tournaments > 0 
                ? Math.round((totalStats.matches / totalStats.tournaments) * 10) / 10 
                : 0;
            
            // Zobrazení statistik ligy
            let html = `
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${totalStats.players}</div>
                        <div class="stat-label">Hráčů celkem</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${totalStats.tournaments}</div>
                        <div class="stat-label">Turnajů celkem</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${totalStats.matches}</div>
                        <div class="stat-label">Zápasů celkem</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${avgTournaments}</div>
                        <div class="stat-label">Průměr turnajů na hráče</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${avgMatches}</div>
                        <div class="stat-label">Průměr zápasů na turnaj</div>
                    </div>
                </div>
                
                <div class="top-players">
                    <h4>Nejlepší hráči</h4>
                    <p><strong>Nejvíce výher:</strong> ${totalStats.topPlayer.name} (${totalStats.topPlayer.wins} výher)</p>
                    <p><strong>Nejaktivnější hráč:</strong> ${totalStats.mostActive.name} (${totalStats.mostActive.tournaments} turnajů)</p>
                </div>
            `;
            
            statsContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba při načítání statistik ligy:', error);
            statsContainer.innerHTML = `<p>Nepodařilo se načíst statistiky ligy: ${error.message}</p>`;
        });
}

/**
 * Převede status turnaje na čitelný text
 * 
 * @param {string} status Status turnaje
 * @return {string} Čitelný text statusu
 */
function getStatusText(status) {
    switch (status) {
        case 'pending':
            return 'Čeká na zahájení';
        case 'underway':
            return 'Probíhá';
        case 'complete':
            return 'Dokončeno';
        default:
            return 'Neurčeno';
    }
}
