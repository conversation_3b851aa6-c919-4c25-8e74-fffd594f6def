<?php
/**
 * Jed<PERSON>duch<PERSON> skript pro nahrání souborů na server
 * Spustit lokálně a pak nahrát výsledek
 */

$files = [
    'get-category-standings.php',
    'sync-and-evaluate-tournaments.php', 
    'test-challonge-sync.php',
    'update-database-for-challonge.php',
    'get-challonge-stats.php'
];

echo "=== SOUBORY PRO NAHRÁNÍ NA SERVER ===\n\n";

foreach ($files as $filename) {
    $filepath = "php/{$filename}";
    
    if (file_exists($filepath)) {
        echo "=== {$filename} ===\n";
        echo "Velikost: " . filesize($filepath) . " bytů\n";
        echo "Obsah:\n";
        echo "```php\n";
        echo file_get_contents($filepath);
        echo "\n```\n\n";
        echo "Příkaz pro vytvoření na serveru:\n";
        echo "cat > /var/www/html/php/{$filename} << 'EOF'\n";
        echo file_get_contents($filepath);
        echo "\nEOF\n\n";
        echo "chmod 644 /var/www/html/php/{$filename}\n\n";
        echo "=" . str_repeat("=", 50) . "\n\n";
    } else {
        echo "CHYBA: Soubor {$filepath} neexistuje!\n\n";
    }
}

echo "=== KONEC ===\n";
?>
