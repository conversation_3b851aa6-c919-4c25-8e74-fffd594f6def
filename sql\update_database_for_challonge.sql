-- Aktualizace databáze pro Challonge integraci
-- <PERSON>pustit na serveru pro přidání chyběj<PERSON><PERSON><PERSON><PERSON> sloupců a tabulek

-- <PERSON><PERSON><PERSON><PERSON><PERSON> sloupce participants_count do tabulky tournaments (pokud neexistuje)
ALTER TABLE tournaments 
ADD COLUMN IF NOT EXISTS participants_count INT DEFAULT 0;

-- <PERSON><PERSON><PERSON><PERSON><PERSON> sloupce points do tabulky tournament_participants (pokud neexistuje)
ALTER TABLE tournament_participants 
ADD COLUMN IF NOT EXISTS points INT DEFAULT 0;

-- <PERSON><PERSON><PERSON><PERSON><PERSON> sloupce name do tabulky tournament_participants (pokud neexistuje)
ALTER TABLE tournament_participants 
ADD COLUMN IF NOT EXISTS name VARCHAR(255) NOT NULL DEFAULT '';

-- Změna player_id na nullable (pokud není)
ALTER TABLE tournament_participants 
MODIFY COLUMN player_id INT NULL;

-- Změna start_date na DATETIME (pokud je DATE)
ALTER TABLE tournaments 
MODIFY COLUMN start_date DATETIME NULL;

-- <PERSON><PERSON><PERSON>ání UNIQUE indexu na challonge_id (pokud neexistuje)
ALTER TABLE tournaments 
ADD CONSTRAINT unique_challonge_id UNIQUE (challonge_id);

-- Přidání UNIQUE indexu na tournament_participants (pokud neexistuje)
ALTER TABLE tournament_participants 
ADD CONSTRAINT unique_participant UNIQUE (tournament_id, challonge_participant_id);

-- Kontrola a vložení kategorií (pokud neexistují)
INSERT IGNORE INTO tournament_categories (name, description) VALUES
('Blue Oyster Cup', 'Úterní turnaje Blue Oyster Cup'),
('Sob Cup', 'Čtvrteční turnaje Sob Cup'),
('Roští Cup', 'Střední turnaje Roští Cup'),
('Speciální turnaje', 'Ostatní a speciální turnaje');

-- Kontrola a vložení nastavení (pokud neexistují)
INSERT IGNORE INTO settings (setting_key, setting_value) VALUES
('last_sync_time', NULL),
('challonge_api_key', 'adbPU7e6QlOzIvxzSDnohavu61YwTNhl6FIDkF3H'),
('challonge_username', 'Rezexil');

-- Zobrazení aktuálního stavu tabulek
SELECT 'tournament_categories' as table_name, COUNT(*) as count FROM tournament_categories
UNION ALL
SELECT 'tournaments', COUNT(*) FROM tournaments
UNION ALL
SELECT 'tournament_participants', COUNT(*) FROM tournament_participants
UNION ALL
SELECT 'settings', COUNT(*) FROM settings;

-- Zobrazení kategorií
SELECT * FROM tournament_categories;

-- Zobrazení nastavení
SELECT * FROM settings WHERE setting_key LIKE 'challonge%' OR setting_key = 'last_sync_time';
