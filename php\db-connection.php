<?php
/**
 * Připojení k databázi
 */

// Konfigurace databáze
$dbHost = 'localhost';
$dbName = 'bruntalska_sipkova_liga';
$dbUser = 'sipky_user';
$dbPass = 'sipky_password';

try {
    // Vytvoření PDO instance
    $pdo = new PDO(
        "mysql:host={$dbHost};dbname={$dbName};charset=utf8mb4",
        $dbUser,
        $dbPass,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
} catch (PDOException $e) {
    // V produkčním prostředí by se chyba neměla zobrazovat uživateli
    die("Nepodařilo se připojit k databázi: " . $e->getMessage());
}
