<?php
/**
 * Skript pro nastavení databáze
 */

// Konfigurace databáze
$dbHost = 'localhost';
$dbUser = 'sipky_user';
$dbPass = 'sipky_password';
$dbName = 'bruntalska_sipkova_liga';

try {
    // Připojení k MySQL serveru
    $pdo = new PDO("mysql:host={$dbHost}", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Vytvoření databáze
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$dbName}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "Databáze '{$dbName}' byla úspěšně vytvořena nebo již existuje.<br>";

    // Připojení k vytvořené databázi
    $pdo->exec("USE `{$dbName}`");

    // Načtení a spuštění SQL skriptu
    $sql = file_get_contents('sql/database.sql');
    $pdo->exec($sql);

    echo "Struktura databáze byla úspěšně vytvořena.<br>";

    // Vytvoření administrátorského účtu
    $username = 'admin';
    $password = password_hash('admin123', PASSWORD_DEFAULT);
    $email = '<EMAIL>';

    // Kontrola, zda uživatel již existuje
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
    $stmt->execute([$username]);

    if (!$stmt->fetch()) {
        $stmt = $pdo->prepare("INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, 'admin')");
        $stmt->execute([$username, $password, $email]);
        echo "Administrátorský účet byl úspěšně vytvořen.<br>";
    } else {
        echo "Administrátorský účet již existuje.<br>";
    }

    echo "Nastavení databáze bylo úspěšně dokončeno.";

} catch (PDOException $e) {
    die("Chyba při nastavování databáze: " . $e->getMessage());
}
