@echo off
echo Nahrávání souborů na server...

REM Nahrání souborů
pscp -i "c:\users\<USER>\iok.private.ppk" -batch index.html ubuntu@158.179.205.158:
pscp -i "c:\users\<USER>\iok.private.ppk" -batch *.php ubuntu@158.179.205.158:
pscp -i "c:\users\<USER>\iok.private.ppk" -batch -r css ubuntu@158.179.205.158:
pscp -i "c:\users\<USER>\iok.private.ppk" -batch -r js ubuntu@158.179.205.158:

REM Přesun souborů do webového adresáře
plink -i "c:\users\<USER>\iok.private.ppk" -batch ubuntu@158.179.205.158 "sudo cp *.html /var/www/html/"
plink -i "c:\users\<USER>\iok.private.ppk" -batch ubuntu@158.179.205.158 "sudo cp *.php /var/www/html/"
plink -i "c:\users\<USER>\iok.private.ppk" -batch ubuntu@158.179.205.158 "sudo cp -r css /var/www/html/"
plink -i "c:\users\<USER>\iok.private.ppk" -batch ubuntu@158.179.205.158 "sudo cp -r js /var/www/html/"
plink -i "c:\users\<USER>\iok.private.ppk" -batch ubuntu@158.179.205.158 "sudo chown -R www-data:www-data /var/www/html/"
plink -i "c:\users\<USER>\iok.private.ppk" -batch ubuntu@158.179.205.158 "sudo chmod -R 755 /var/www/html/"

echo Hotovo!
pause
