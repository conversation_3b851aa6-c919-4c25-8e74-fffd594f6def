<?php
/**
 * Skript pro získání nastavení Challonge API
 */

// Spuštění session
session_start();

// Kontrola, zda je uživatel přihlášen
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('HTTP/1.1 401 Unauthorized');
    echo json_encode(['error' => 'Nejste přihlášen']);
    exit;
}

// Připojení k databázi
require_once 'db-connection.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

try {
    // Získání nastavení Challonge API
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('challonge_api_key', 'challonge_username')");
    $stmt->execute();
    $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    
    // Vrácení nastavení jako J<PERSON>N
    echo json_encode([
        'api_key' => isset($settings['challonge_api_key']) ? $settings['challonge_api_key'] : '',
        'username' => isset($settings['challonge_username']) ? $settings['challonge_username'] : ''
    ]);
} catch (PDOException $e) {
    // Vrácení chybové zprávy jako JSON
    http_response_code(500);
    echo json_encode(['error' => 'Nepodařilo se získat nastavení: ' . $e->getMessage()]);
}
