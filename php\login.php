<?php
/**
 * Skript pro přihlášení do administrace
 */

// Spuštění session
session_start();

// Připojení k databázi
require_once 'db-connection.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

// Kontrola, zda byl odeslán formulář
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Neplatný požadavek']);
    exit;
}

// Získán<PERSON> přihlašovacích údajů
$username = isset($_POST['username']) ? trim($_POST['username']) : '';
$password = isset($_POST['password']) ? $_POST['password'] : '';

// Kontrola, zda byly zad<PERSON><PERSON> p<PERSON>šovací údaje
if (empty($username) || empty($password)) {
    echo json_encode(['success' => false, 'message' => 'Zadejte uživatelské jméno a heslo']);
    exit;
}

try {
    // Vyhledání uživatele v databázi
    $stmt = $pdo->prepare("SELECT id, username, password, role FROM users WHERE username = ?");
    $stmt->execute([$username]);
    $user = $stmt->fetch();
    
    // Kontrola, zda uživatel existuje a heslo je správné
    if (!$user || !password_verify($password, $user['password'])) {
        echo json_encode(['success' => false, 'message' => 'Neplatné přihlašovací údaje']);
        exit;
    }
    
    // Uložení informací o přihlášeném uživateli do session
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['role'] = $user['role'];
    $_SESSION['logged_in'] = true;
    
    // Vrácení úspěšné odpovědi
    echo json_encode(['success' => true, 'message' => 'Přihlášení úspěšné']);
} catch (PDOException $e) {
    // Vrácení chybové zprávy jako JSON
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Chyba při přihlašování: ' . $e->getMessage()]);
}
