<?php
/**
 * Skript pro získání statistik a výsledků z Challonge turnajů
 */

// Připojení k databázi
require_once 'db-connection.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

try {
    // Získání parametrů
    $category = $_GET['category'] ?? 'all';
    $limit = (int) ($_GET['limit'] ?? 50);
    
    // Základní statistiky
    $stats = [];
    
    // Celkový počet turnajů podle kategorií
    $stmt = $pdo->query("
        SELECT 
            tc.name as category_name,
            COUNT(t.id) as tournament_count,
            SUM(t.participants_count) as total_participants
        FROM tournaments t
        JOIN tournament_categories tc ON t.category_id = tc.id
        GROUP BY tc.id, tc.name
        ORDER BY tournament_count DESC
    ");
    $stats['categories'] = $stmt->fetchAll();
    
    // Nejaktivn<PERSON><PERSON><PERSON><PERSON> hr<PERSON> (podle počtu turnajů)
    $stmt = $pdo->prepare("
        SELECT 
            tp.name,
            COUNT(tp.id) as tournaments_played,
            SUM(tp.points) as total_points,
            AVG(tp.points) as avg_points,
            MIN(tp.final_rank) as best_rank,
            MAX(tp.final_rank) as worst_rank
        FROM tournament_participants tp
        JOIN tournaments t ON tp.tournament_id = t.id
        " . ($category !== 'all' ? "JOIN tournament_categories tc ON t.category_id = tc.id WHERE tc.name = ?" : "") . "
        GROUP BY tp.name
        HAVING tournaments_played >= 3
        ORDER BY total_points DESC, tournaments_played DESC
        LIMIT ?
    ");
    
    if ($category !== 'all') {
        $stmt->execute([$category, $limit]);
    } else {
        $stmt->execute([$limit]);
    }
    $stats['top_players'] = $stmt->fetchAll();
    
    // Nejnovější turnaje
    $stmt = $pdo->prepare("
        SELECT 
            t.name,
            t.start_date,
            t.participants_count,
            t.status,
            tc.name as category_name,
            COUNT(tp.id) as processed_participants
        FROM tournaments t
        JOIN tournament_categories tc ON t.category_id = tc.id
        LEFT JOIN tournament_participants tp ON t.id = tp.tournament_id
        " . ($category !== 'all' ? "WHERE tc.name = ?" : "") . "
        GROUP BY t.id
        ORDER BY t.start_date DESC
        LIMIT ?
    ");
    
    if ($category !== 'all') {
        $stmt->execute([$category, $limit]);
    } else {
        $stmt->execute([$limit]);
    }
    $stats['recent_tournaments'] = $stmt->fetchAll();
    
    // Statistiky podle kategorií pro konkrétní kategorii
    if ($category !== 'all') {
        // Top 10 v dané kategorii
        $stmt = $pdo->prepare("
            SELECT 
                tp.name,
                COUNT(tp.id) as tournaments_played,
                SUM(tp.points) as total_points,
                AVG(tp.points) as avg_points,
                COUNT(CASE WHEN tp.final_rank = 1 THEN 1 END) as wins,
                COUNT(CASE WHEN tp.final_rank <= 3 THEN 1 END) as podiums
            FROM tournament_participants tp
            JOIN tournaments t ON tp.tournament_id = t.id
            JOIN tournament_categories tc ON t.category_id = tc.id
            WHERE tc.name = ?
            GROUP BY tp.name
            HAVING tournaments_played >= 2
            ORDER BY total_points DESC
            LIMIT 10
        ");
        $stmt->execute([$category]);
        $stats['category_leaderboard'] = $stmt->fetchAll();
        
        // Nejlepší výsledky v kategorii
        $stmt = $pdo->prepare("
            SELECT 
                tp.name,
                t.name as tournament_name,
                tp.final_rank,
                tp.points,
                t.start_date,
                t.participants_count
            FROM tournament_participants tp
            JOIN tournaments t ON tp.tournament_id = t.id
            JOIN tournament_categories tc ON t.category_id = tc.id
            WHERE tc.name = ? AND tp.final_rank <= 3
            ORDER BY tp.final_rank ASC, t.start_date DESC
            LIMIT 20
        ");
        $stmt->execute([$category]);
        $stats['best_results'] = $stmt->fetchAll();
    }
    
    // Celkové statistiky
    $stmt = $pdo->query("
        SELECT 
            COUNT(DISTINCT t.id) as total_tournaments,
            COUNT(DISTINCT tp.name) as unique_players,
            SUM(tp.points) as total_points_awarded,
            AVG(t.participants_count) as avg_tournament_size,
            MAX(t.participants_count) as largest_tournament
        FROM tournaments t
        LEFT JOIN tournament_participants tp ON t.id = tp.tournament_id
    ");
    $stats['overall'] = $stmt->fetch();
    
    // Poslední synchronizace
    $stmt = $pdo->query("SELECT setting_value FROM settings WHERE setting_key = 'last_sync_time'");
    $lastSync = $stmt->fetchColumn();
    $stats['last_sync'] = $lastSync;
    
    // Vrácení statistik
    echo json_encode([
        'success' => true,
        'category' => $category,
        'stats' => $stats
    ]);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Chyba při získávání statistik: ' . $e->getMessage()
    ]);
}
?>
