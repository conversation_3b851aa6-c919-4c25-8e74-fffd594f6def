<?php
/**
 * Skript pro získ<PERSON>í účastníků turnaje
 */

// Připojení k <PERSON>bázi
require_once 'db-connection.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

// Kontrola, zda byl zadán ID turnaje
if (!isset($_GET['tournament_id']) || !is_numeric($_GET['tournament_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Chybí ID turnaje']);
    exit;
}

$tournamentId = (int) $_GET['tournament_id'];

try {
    // Získání účastníků turnaje
    $query = "
        SELECT 
            p.*,
            tp.seed,
            tp.final_rank
        FROM 
            tournament_participants tp
        JOIN 
            players p ON tp.player_id = p.id
        WHERE 
            tp.tournament_id = ?
        ORDER BY 
            tp.final_rank IS NULL, tp.final_rank, tp.seed, p.name
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute([$tournamentId]);
    $participants = $stmt->fetchAll();
    
    // Vrácení účastníků turnaje jako JSON
    echo json_encode($participants);
} catch (PDOException $e) {
    // Vrácení chybové zprávy jako JSON
    http_response_code(500);
    echo json_encode(['error' => 'Nepodařilo se získat účastníky turnaje: ' . $e->getMessage()]);
}
