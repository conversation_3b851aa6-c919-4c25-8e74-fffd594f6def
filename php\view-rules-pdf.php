<?php
/**
 * Skript pro zobrazení PDF souboru s pravidly
 */

// Připojení k databázi
require_once 'db-connection.php';

// Kontrola, zda byl zadán ID
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('HTTP/1.1 400 Bad Request');
    echo 'Chybný požadavek: Chybí ID pravidla';
    exit;
}

$id = (int) $_GET['id'];

try {
    // Získání cesty k PDF souboru
    $stmt = $pdo->prepare("SELECT pdf_path, title FROM rules WHERE id = ?");
    $stmt->execute([$id]);
    $rule = $stmt->fetch();
    
    if (!$rule) {
        header('HTTP/1.1 404 Not Found');
        echo 'Pravidlo nebylo nalezeno';
        exit;
    }
    
    if (empty($rule['pdf_path']) || !file_exists('../' . $rule['pdf_path'])) {
        header('HTTP/1.1 404 Not Found');
        echo 'PDF soubor neexistuje';
        exit;
    }
    
    // Nastavení hlaviček pro PDF
    header('Content-Type: application/pdf');
    header('Content-Disposition: inline; filename="' . basename($rule['pdf_path']) . '"');
    header('Content-Transfer-Encoding: binary');
    header('Accept-Ranges: bytes');
    
    // Výpis obsahu PDF souboru
    readfile('../' . $rule['pdf_path']);
} catch (PDOException $e) {
    header('HTTP/1.1 500 Internal Server Error');
    echo 'Chyba při načítání PDF souboru: ' . $e->getMessage();
}
