<?php
/**
 * Skript pro získání pravidel
 */

// Připojení k databázi
require_once 'db-connection.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

// Určení formátu (text nebo PDF)
$format = isset($_GET['format']) ? $_GET['format'] : 'text';

try {
    if ($format === 'pdf') {
        // Získání seznamu PDF dokumentů s pravidly
        $stmt = $pdo->prepare("SELECT id, title, pdf_path FROM rules WHERE pdf_path IS NOT NULL ORDER BY id DESC");
        $stmt->execute();
        $rules = $stmt->fetchAll();
    } else {
        // Získání textových pravidel
        $stmt = $pdo->prepare("SELECT id, title, content FROM rules ORDER BY id DESC");
        $stmt->execute();
        $rules = $stmt->fetchAll();
    }
    
    // Vrácení pravidel jako JSON
    echo json_encode($rules);
} catch (PDOException $e) {
    // Vrácení chybové zprávy jako JSON
    http_response_code(500);
    echo json_encode(['error' => 'Nepodařilo se získat pravidla: ' . $e->getMessage()]);
}
