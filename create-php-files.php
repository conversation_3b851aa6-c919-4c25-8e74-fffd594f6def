<?php
/**
 * Skript pro vytvoření PHP souborů na serveru
 * Nahrajte tento soubor na server a spusťte ho
 */

// Kontrola bezpečnosti
$password = $_GET['pass'] ?? '';
if ($password !== 'bruntal2024') {
    die('N<PERSON>latn<PERSON> heslo');
}

echo "<h1>Vytváření PHP souborů pro Challonge</h1>\n";

// Soubor 1: get-category-standings.php
$file1_content = '<?php
/**
 * Skript pro získání tabulky podle kategorie turnajů s body a účastmi
 */

// Připojení k databázi
require_once \'db-connection.php\';

// Nastavení hlavičky pro JSON odpověď
header(\'Content-Type: application/json\');

try {
    // Získání parametrů
    $category = $_GET[\'category\'] ?? \'Blue Oyster Cup\';
    $limit = (int) ($_GET[\'limit\'] ?? 20);
    
    // Simulace dat pro testování
    $bocPlayers = [
        [\'name\' => \'Libor\', \'tournaments_played\' => 35, \'total_points\' => 2450, \'avg_points\' => 70, \'wins\' => 8, \'podiums\' => 18, \'top8\' => 28, \'position\' => 1],
        [\'name\' => \'Miška\', \'tournaments_played\' => 32, \'total_points\' => 2180, \'avg_points\' => 68, \'wins\' => 6, \'podiums\' => 16, \'top8\' => 25, \'position\' => 2],
        [\'name\' => \'Pepa\', \'tournaments_played\' => 28, \'total_points\' => 1890, \'avg_points\' => 67, \'wins\' => 5, \'podiums\' => 14, \'top8\' => 22, \'position\' => 3],
        [\'name\' => \'Tomáš\', \'tournaments_played\' => 30, \'total_points\' => 1820, \'avg_points\' => 61, \'wins\' => 4, \'podiums\' => 12, \'top8\' => 20, \'position\' => 4],
        [\'name\' => \'Pavel\', \'tournaments_played\' => 25, \'total_points\' => 1650, \'avg_points\' => 66, \'wins\' => 3, \'podiums\' => 11, \'top8\' => 18, \'position\' => 5],
        [\'name\' => \'Martin\', \'tournaments_played\' => 27, \'total_points\' => 1580, \'avg_points\' => 59, \'wins\' => 3, \'podiums\' => 10, \'top8\' => 17, \'position\' => 6],
        [\'name\' => \'Honza\', \'tournaments_played\' => 24, \'total_points\' => 1420, \'avg_points\' => 59, \'wins\' => 2, \'podiums\' => 9, \'top8\' => 16, \'position\' => 7],
        [\'name\' => \'David\', \'tournaments_played\' => 22, \'total_points\' => 1320, \'avg_points\' => 60, \'wins\' => 2, \'podiums\' => 8, \'top8\' => 15, \'position\' => 8],
        [\'name\' => \'Jakub\', \'tournaments_played\' => 20, \'total_points\' => 1180, \'avg_points\' => 59, \'wins\' => 1, \'podiums\' => 7, \'top8\' => 14, \'position\' => 9],
        [\'name\' => \'Lukáš\', \'tournaments_played\' => 18, \'total_points\' => 1050, \'avg_points\' => 58, \'wins\' => 1, \'podiums\' => 6, \'top8\' => 12, \'position\' => 10],
        [\'name\' => \'Michal\', \'tournaments_played\' => 16, \'total_points\' => 920, \'avg_points\' => 58, \'wins\' => 1, \'podiums\' => 5, \'top8\' => 11, \'position\' => 11],
        [\'name\' => \'Ondřej\', \'tournaments_played\' => 15, \'total_points\' => 840, \'avg_points\' => 56, \'wins\' => 0, \'podiums\' => 4, \'top8\' => 10, \'position\' => 12],
        [\'name\' => \'Radek\', \'tournaments_played\' => 14, \'total_points\' => 770, \'avg_points\' => 55, \'wins\' => 0, \'podiums\' => 3, \'top8\' => 9, \'position\' => 13],
        [\'name\' => \'Jiří\', \'tournaments_played\' => 12, \'total_points\' => 660, \'avg_points\' => 55, \'wins\' => 0, \'podiums\' => 3, \'top8\' => 8, \'position\' => 14],
        [\'name\' => \'Václav\', \'tournaments_played\' => 11, \'total_points\' => 580, \'avg_points\' => 53, \'wins\' => 0, \'podiums\' => 2, \'top8\' => 7, \'position\' => 15],
        [\'name\' => \'Aleš\', \'tournaments_played\' => 10, \'total_points\' => 520, \'avg_points\' => 52, \'wins\' => 0, \'podiums\' => 2, \'top8\' => 6, \'position\' => 16],
        [\'name\' => \'Filip\', \'tournaments_played\' => 9, \'total_points\' => 450, \'avg_points\' => 50, \'wins\' => 0, \'podiums\' => 1, \'top8\' => 5, \'position\' => 17],
        [\'name\' => \'Roman\', \'tournaments_played\' => 8, \'total_points\' => 380, \'avg_points\' => 48, \'wins\' => 0, \'podiums\' => 1, \'top8\' => 4, \'position\' => 18],
        [\'name\' => \'Stanislav\', \'tournaments_played\' => 7, \'total_points\' => 320, \'avg_points\' => 46, \'wins\' => 0, \'podiums\' => 1, \'top8\' => 3, \'position\' => 19],
        [\'name\' => \'Zdeněk\', \'tournaments_played\' => 6, \'total_points\' => 260, \'avg_points\' => 43, \'wins\' => 0, \'podiums\' => 0, \'top8\' => 2, \'position\' => 20]
    ];
    
    // Přidání dalších statistik
    foreach ($bocPlayers as &$player) {
        $player[\'win_rate\'] = $player[\'tournaments_played\'] > 0 ? round(($player[\'wins\'] / $player[\'tournaments_played\']) * 100, 1) : 0;
        $player[\'podium_rate\'] = $player[\'tournaments_played\'] > 0 ? round(($player[\'podiums\'] / $player[\'tournaments_played\']) * 100, 1) : 0;
        $player[\'top8_rate\'] = $player[\'tournaments_played\'] > 0 ? round(($player[\'top8\'] / $player[\'tournaments_played\']) * 100, 1) : 0;
        $player[\'best_rank\'] = 1;
        $player[\'worst_rank\'] = 32;
        $player[\'tournament_history\'] = [];
    }
    
    $standings = array_slice($bocPlayers, 0, $limit);
    
    $overallStats = [
        \'total_tournaments\' => 35,
        \'unique_players\' => count($bocPlayers),
        \'total_points_awarded\' => array_sum(array_column($bocPlayers, \'total_points\')),
        \'avg_tournament_size\' => 16
    ];
    
    $recentTournaments = [
        [\'name\' => \'BOC 36. kolo\', \'start_date\' => \'2025-05-27\', \'participants_count\' => 7, \'winner\' => \'Libor\', \'status\' => \'complete\'],
        [\'name\' => \'BOC 35. kolo\', \'start_date\' => \'2025-05-20\', \'participants_count\' => 10, \'winner\' => \'Miška\', \'status\' => \'complete\'],
        [\'name\' => \'BOC 34. kolo\', \'start_date\' => \'2025-05-13\', \'participants_count\' => 13, \'winner\' => \'Pepa\', \'status\' => \'complete\'],
        [\'name\' => \'BOC 33. kolo\', \'start_date\' => \'2025-05-06\', \'participants_count\' => 8, \'winner\' => \'Tomáš\', \'status\' => \'complete\'],
        [\'name\' => \'BOC 32. kolo\', \'start_date\' => \'2025-04-29\', \'participants_count\' => 12, \'winner\' => \'Pavel\', \'status\' => \'complete\']
    ];
    
    echo json_encode([
        \'success\' => true,
        \'category\' => $category,
        \'standings\' => $standings,
        \'overall_stats\' => $overallStats,
        \'recent_tournaments\' => $recentTournaments,
        \'last_updated\' => date(\'Y-m-d H:i:s\')
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        \'success\' => false,
        \'message\' => \'Chyba při získávání tabulky: \' . $e->getMessage(),
        \'standings\' => []
    ]);
}
?>';

if (file_put_contents('/var/www/html/php/get-category-standings.php', $file1_content)) {
    echo "<p>✅ Soubor get-category-standings.php byl vytvořen</p>\n";
    chmod('/var/www/html/php/get-category-standings.php', 0644);
} else {
    echo "<p>❌ Chyba při vytváření get-category-standings.php</p>\n";
}

echo "<p>Hotovo! Soubory byly vytvořeny.</p>\n";
echo "<p><a href='../blue-oyster-cup.html'>Otestovat Blue Oyster Cup stránku</a></p>\n";
?>
