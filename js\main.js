document.addEventListener('DOMContentLoaded', function() {
    // Načtení statistik hráčů na hlavní stránce
    if (document.getElementById('players-stats')) {
        fetchPlayersStats();
    }

    // Načtení nadcházej<PERSON><PERSON><PERSON>ch turnajů na hlavní stránce
    if (document.getElementById('upcoming-tournaments')) {
        fetchUpcomingTournaments();
    }

    // Nastavení hover efektů pro menu
    setupMenuHoverEffects();
});

/**
 * Nastaví hover efekty pro menu - pouze pro tlačítka
 */
function setupMenuHoverEffects() {
    // Hover efekty jsou nyní řešeny pouze v CSS
    // Žádné JavaScript hover efekty pro pozadí
}

/**
 * Načte statistiky hráčů z API
 */
function fetchPlayersStats() {
    const statsContainer = document.getElementById('players-stats');

    fetch('php/get-players-stats.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se načíst statistiky hráčů');
            }
            return response.json();
        })
        .then(data => {
            if (data.length === 0) {
                statsContainer.innerHTML = '<p>Žádné statistiky k zobrazení</p>';
                return;
            }

            // Vytvoření tabulky s hráči
            let html = `
                <table>
                    <thead>
                        <tr>
                            <th>Jméno</th>
                            <th>Odehrané turnaje</th>
                            <th>Výhry</th>
                            <th>Prohry</th>
                            <th>Úspěšnost</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            // Omezení na top 5 hráčů
            const topPlayers = data.slice(0, 5);

            topPlayers.forEach(player => {
                const winRate = player.matches > 0
                    ? Math.round((player.wins / player.matches) * 100)
                    : 0;

                html += `
                    <tr>
                        <td><a href="hrac.html?id=${player.id}">${player.name}</a></td>
                        <td>${player.tournaments}</td>
                        <td>${player.wins}</td>
                        <td>${player.losses}</td>
                        <td>${winRate}%</td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                </table>
                <p class="text-center mt-3">
                    <a href="hraci.html" class="btn">Zobrazit všechny hráče</a>
                </p>
            `;

            statsContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba při načítání statistik hráčů:', error);
            statsContainer.innerHTML = `<p>Nepodařilo se načíst statistiky hráčů: ${error.message}</p>`;
        });
}

/**
 * Načte nadcházející turnaje z API
 */
function fetchUpcomingTournaments() {
    const tournamentsContainer = document.getElementById('upcoming-tournaments');

    fetch('php/get-tournaments.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se načíst turnaje');
            }
            return response.json();
        })
        .then(data => {
            if (data.length === 0) {
                tournamentsContainer.innerHTML = '<p>Žádné nadcházející turnaje</p>';
                return;
            }

            // Filtrování nadcházejících turnajů (max 3)
            const upcomingTournaments = data
                .filter(tournament => new Date(tournament.start_date) > new Date())
                .sort((a, b) => new Date(a.start_date) - new Date(b.start_date))
                .slice(0, 3);

            if (upcomingTournaments.length === 0) {
                tournamentsContainer.innerHTML = '<p>Žádné nadcházející turnaje</p>';
                return;
            }

            let html = '<div class="tournament-list">';

            upcomingTournaments.forEach(tournament => {
                const startDate = new Date(tournament.start_date);
                const formattedDate = startDate.toLocaleDateString('cs-CZ');

                html += `
                    <div class="tournament-card">
                        <h3>${tournament.name}</h3>
                        <p><strong>Datum:</strong> ${formattedDate}</p>
                        <p><strong>Typ:</strong> ${tournament.tournament_type}</p>
                        <a href="turnaj.html?id=${tournament.id}" class="btn">Detaily turnaje</a>
                    </div>
                `;
            });

            html += `
                </div>
                <p class="text-center mt-3">
                    <a href="turnaje.html" class="btn">Zobrazit všechny turnaje</a>
                </p>
            `;

            tournamentsContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba při načítání turnajů:', error);
            tournamentsContainer.innerHTML = `<p>Nepodařilo se načíst turnaje: ${error.message}</p>`;
        });
}
