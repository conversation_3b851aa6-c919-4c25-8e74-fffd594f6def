<?php
/**
 * Skript pro získání tabulky Blue Oyster Cup s body a účastmi
 */

// Připojení k <PERSON>b<PERSON>zi
require_once 'db-connection.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

try {
    // Získání parametrů
    $season = $_GET['season'] ?? '2024-25';
    $limit = (int) ($_GET['limit'] ?? 100);
    
    // Získání ID kategorie Blue Oyster Cup
    $stmt = $pdo->prepare("SELECT id FROM tournament_categories WHERE name = 'Blue Oyster Cup'");
    $stmt->execute();
    $bocCategoryId = $stmt->fetchColumn();
    
    if (!$bocCategoryId) {
        echo json_encode([
            'success' => false,
            'message' => 'Kategorie Blue Oyster Cup nebyla nalezena',
            'standings' => []
        ]);
        exit;
    }
    
    // Hlavní dotaz pro BOC tabulku
    $query = "
        SELECT 
            tp.name,
            COUNT(tp.id) as tournaments_played,
            SUM(tp.points) as total_points,
            AVG(tp.points) as avg_points,
            MIN(tp.final_rank) as best_rank,
            MAX(tp.final_rank) as worst_rank,
            COUNT(CASE WHEN tp.final_rank = 1 THEN 1 END) as wins,
            COUNT(CASE WHEN tp.final_rank <= 3 THEN 1 END) as podiums,
            COUNT(CASE WHEN tp.final_rank <= 8 THEN 1 END) as top8,
            GROUP_CONCAT(
                CONCAT(t.name, ':', tp.final_rank, ':', tp.points) 
                ORDER BY t.start_date DESC 
                SEPARATOR '|'
            ) as tournament_details
        FROM tournament_participants tp
        JOIN tournaments t ON tp.tournament_id = t.id
        WHERE t.category_id = ?
        AND t.status = 'complete'
        GROUP BY tp.name
        HAVING tournaments_played > 0
        ORDER BY total_points DESC, tournaments_played DESC, avg_points DESC, best_rank ASC
        LIMIT ?
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute([$bocCategoryId, $limit]);
    $standings = $stmt->fetchAll();
    
    // Zpracování výsledků
    $processedStandings = [];
    $position = 1;
    
    foreach ($standings as $player) {
        // Zpracování detailů turnajů
        $tournamentHistory = [];
        if ($player['tournament_details']) {
            $details = explode('|', $player['tournament_details']);
            foreach ($details as $detail) {
                $parts = explode(':', $detail);
                if (count($parts) >= 3) {
                    $tournamentHistory[] = [
                        'tournament_name' => $parts[0],
                        'final_rank' => (int)$parts[1],
                        'points' => (int)$parts[2]
                    ];
                }
            }
        }
        
        // Výpočet úspěšnosti
        $winRate = $player['tournaments_played'] > 0 
            ? round(($player['wins'] / $player['tournaments_played']) * 100, 1)
            : 0;
            
        $podiumRate = $player['tournaments_played'] > 0 
            ? round(($player['podiums'] / $player['tournaments_played']) * 100, 1)
            : 0;
        
        $processedStandings[] = [
            'position' => $position,
            'name' => $player['name'],
            'tournaments_played' => (int)$player['tournaments_played'],
            'total_points' => (int)$player['total_points'],
            'avg_points' => round($player['avg_points'], 1),
            'best_rank' => (int)$player['best_rank'],
            'worst_rank' => (int)$player['worst_rank'],
            'wins' => (int)$player['wins'],
            'podiums' => (int)$player['podiums'],
            'top8' => (int)$player['top8'],
            'win_rate' => $winRate,
            'podium_rate' => $podiumRate,
            'tournament_history' => $tournamentHistory
        ];
        
        $position++;
    }
    
    // Získání dalších statistik
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(DISTINCT t.id) as total_tournaments,
            COUNT(DISTINCT tp.name) as unique_players,
            SUM(tp.points) as total_points_awarded,
            AVG(t.participants_count) as avg_tournament_size
        FROM tournaments t
        LEFT JOIN tournament_participants tp ON t.id = tp.tournament_id
        WHERE t.category_id = ? AND t.status = 'complete'
    ");
    $stmt->execute([$bocCategoryId]);
    $overallStats = $stmt->fetch();
    
    // Nejnovější turnaje
    $stmt = $pdo->prepare("
        SELECT 
            t.name,
            t.start_date,
            t.participants_count,
            t.status
        FROM tournaments t
        WHERE t.category_id = ?
        ORDER BY t.start_date DESC
        LIMIT 5
    ");
    $stmt->execute([$bocCategoryId]);
    $recentTournaments = $stmt->fetchAll();
    
    // Vrácení výsledků
    echo json_encode([
        'success' => true,
        'category' => 'Blue Oyster Cup',
        'season' => $season,
        'standings' => $processedStandings,
        'overall_stats' => $overallStats,
        'recent_tournaments' => $recentTournaments,
        'last_updated' => date('Y-m-d H:i:s')
    ]);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Chyba při získávání BOC tabulky: ' . $e->getMessage(),
        'standings' => []
    ]);
}
?>
