<?php
/**
 * Skript pro získání statistik pro administraci
 */

// Spuštění session
session_start();

// <PERSON>ntrola, zda je uživatel přihlášen
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('HTTP/1.1 401 Unauthorized');
    echo json_encode(['error' => 'Nejste přihlášen']);
    exit;
}

// Připojení k databázi
require_once 'db-connection.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

try {
    // Získání počtu hráčů
    $stmt = $pdo->query("SELECT COUNT(*) AS count FROM players");
    $playersCount = $stmt->fetch()['count'];
    
    // Získání počtu turnajů
    $stmt = $pdo->query("SELECT COUNT(*) AS count FROM tournaments");
    $tournamentsCount = $stmt->fetch()['count'];
    
    // Získání počtu zápasů
    $stmt = $pdo->query("SELECT COUNT(*) AS count FROM matches");
    $matchesCount = $stmt->fetch()['count'];
    
    // Získání času poslední synchronizace
    $stmt = $pdo->query("SELECT setting_value FROM settings WHERE setting_key = 'last_sync_time'");
    $lastSyncTime = $stmt->fetch();
    $lastSyncTime = $lastSyncTime ? $lastSyncTime['setting_value'] : null;
    
    // Vrácení statistik jako JSON
    echo json_encode([
        'players_count' => $playersCount,
        'tournaments_count' => $tournamentsCount,
        'matches_count' => $matchesCount,
        'last_sync_time' => $lastSyncTime
    ]);
} catch (PDOException $e) {
    // Vrácení chybové zprávy jako JSON
    http_response_code(500);
    echo json_encode(['error' => 'Nepodařilo se získat statistiky: ' . $e->getMessage()]);
}
