<?php
/**
 * Skript pro získání statistik hráčů
 */

// Připojení k databázi
require_once 'db-connection.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

try {
    // Získání statistik hráčů
    $query = "
        SELECT 
            p.id,
            p.name,
            COUNT(DISTINCT tp.tournament_id) AS tournaments,
            COUNT(DISTINCT CASE WHEN m.winner_id = p.id THEN m.id END) AS wins,
            COUNT(DISTINCT CASE WHEN (m.player1_id = p.id OR m.player2_id = p.id) AND m.winner_id != p.id AND m.winner_id IS NOT NULL THEN m.id END) AS losses,
            COUNT(DISTINCT CASE WHEN (m.player1_id = p.id OR m.player2_id = p.id) THEN m.id END) AS matches
        FROM 
            players p
        LEFT JOIN 
            tournament_participants tp ON p.id = tp.player_id
        LEFT JOIN 
            matches m ON (m.player1_id = p.id OR m.player2_id = p.id)
        GROUP BY 
            p.id, p.name
        ORDER BY 
            wins DESC, tournaments DESC, name ASC
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $stats = $stmt->fetchAll();
    
    // Vrácení statistik hráčů jako JSON
    echo json_encode($stats);
} catch (PDOException $e) {
    // Vrácení chybové zprávy jako JSON
    http_response_code(500);
    echo json_encode(['error' => 'Nepodařilo se získat statistiky hráčů: ' . $e->getMessage()]);
}
