<?php
/**
 * Skript pro získání seznamu hráč<PERSON>
 */

// Připojení k databázi
require_once 'db-connection.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

try {
    // Získání seznamu hráčů
    $stmt = $pdo->prepare("SELECT * FROM players ORDER BY name");
    $stmt->execute();
    $players = $stmt->fetchAll();
    
    // Vrácení seznamu hráčů jako JSON
    echo json_encode($players);
} catch (PDOException $e) {
    // Vrácení chybové zprávy jako JSO<PERSON>
    http_response_code(500);
    echo json_encode(['error' => 'Nepodařilo se získat seznam hráčů: ' . $e->getMessage()]);
}
