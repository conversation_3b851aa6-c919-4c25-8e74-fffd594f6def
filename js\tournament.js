document.addEventListener('DOMContentLoaded', function() {
    // Získání ID turnaje z URL parametru
    const urlParams = new URLSearchParams(window.location.search);
    const tournamentId = urlParams.get('id');
    
    if (!tournamentId) {
        // Pokud není zadáno ID turnaje, přesměrujeme na seznam turnajů
        window.location.href = 'turnaje.html';
        return;
    }
    
    // Načtení detailu turnaje
    fetchTournamentDetail(tournamentId);
    
    // Načtení pavouka turnaje
    fetchTournamentBracket(tournamentId);
    
    // Načtení účastníků turnaje
    fetchTournamentParticipants(tournamentId);
    
    // Načtení z<PERSON> turnaje
    fetchTournamentMatches(tournamentId);
    
    // Obsluha přepínání záložek
    setupTabs();
});

/**
 * Nastaví přepínání z<PERSON>lo<PERSON>
 */
function setupTabs() {
    const tabLinks = document.querySelectorAll('.tabs a');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Odstranění aktivní třídy ze všech záložek
            tabLinks.forEach(l => l.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // Přidání aktivní třídy na kliknutou záložku
            this.classList.add('active');
            
            // Zobrazení odpovídajícího obsahu
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });
}

/**
 * Načte detail turnaje z API
 * 
 * @param {number} tournamentId ID turnaje
 */
function fetchTournamentDetail(tournamentId) {
    const tournamentInfoContainer = document.getElementById('tournament-info');
    
    fetch(`php/get-tournament.php?id=${tournamentId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se načíst detail turnaje');
            }
            return response.json();
        })
        .then(data => {
            if (!data) {
                tournamentInfoContainer.innerHTML = '<p>Turnaj nebyl nalezen</p>';
                return;
            }

            // Aktualizace titulku stránky
            document.title = `${data.name} - Bruntálská šipková liga`;
            
            // Formátování dat
            const startDate = data.start_date 
                ? new Date(data.start_date).toLocaleDateString('cs-CZ') 
                : 'Neurčeno';
            const endDate = data.end_date 
                ? new Date(data.end_date).toLocaleDateString('cs-CZ') 
                : 'Neurčeno';
            const status = getStatusText(data.status);
            
            // Zobrazení detailu turnaje
            let html = `
                <h2>${data.name}</h2>
                <div class="tournament-details">
                    <p><strong>Kategorie:</strong> ${data.category_name || 'Nezařazeno'}</p>
                    <p><strong>Datum zahájení:</strong> ${startDate}</p>
                    <p><strong>Datum ukončení:</strong> ${endDate}</p>
                    <p><strong>Status:</strong> ${status}</p>
            `;
            
            if (data.url) {
                html += `<p><strong>Odkaz na Challonge:</strong> <a href="${data.url}" target="_blank">Zobrazit na Challonge.com</a></p>`;
            }
            
            if (data.description) {
                html += `
                    <div class="tournament-description">
                        <h3>Popis turnaje</h3>
                        <p>${data.description}</p>
                    </div>
                `;
            }
            
            html += `</div>`;
            
            tournamentInfoContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba při načítání detailu turnaje:', error);
            tournamentInfoContainer.innerHTML = `<p>Nepodařilo se načíst detail turnaje: ${error.message}</p>`;
        });
}

/**
 * Načte pavouka turnaje z API
 * 
 * @param {number} tournamentId ID turnaje
 */
function fetchTournamentBracket(tournamentId) {
    const bracketContainer = document.getElementById('bracket-content');
    
    fetch(`php/get-tournament.php?id=${tournamentId}&bracket=1`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se načíst pavouka turnaje');
            }
            return response.json();
        })
        .then(data => {
            if (!data || !data.challonge_id) {
                bracketContainer.innerHTML = '<p>Pavouk turnaje není k dispozici</p>';
                return;
            }

            // Zobrazení pavouka turnaje z Challonge
            const challongeId = data.challonge_id;
            
            // Vytvoření iframe s pavoukem z Challonge
            const html = `
                <div class="tournament-bracket">
                    <iframe src="https://challonge.com/${challongeId}/module" width="100%" height="500" frameborder="0" scrolling="auto" allowtransparency="true"></iframe>
                </div>
                <p class="text-center">
                    <a href="https://challonge.com/${challongeId}" target="_blank" class="btn">Zobrazit na Challonge.com</a>
                </p>
            `;
            
            bracketContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba při načítání pavouka turnaje:', error);
            bracketContainer.innerHTML = `<p>Nepodařilo se načíst pavouka turnaje: ${error.message}</p>`;
        });
}

/**
 * Načte účastníky turnaje z API
 * 
 * @param {number} tournamentId ID turnaje
 */
function fetchTournamentParticipants(tournamentId) {
    const participantsContainer = document.getElementById('participants-content');
    
    fetch(`php/get-tournament-participants.php?tournament_id=${tournamentId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se načíst účastníky turnaje');
            }
            return response.json();
        })
        .then(data => {
            if (!data || data.length === 0) {
                participantsContainer.innerHTML = '<p>Žádní účastníci k zobrazení</p>';
                return;
            }

            // Zobrazení účastníků turnaje
            let html = `
                <table>
                    <thead>
                        <tr>
                            <th>Jméno</th>
                            <th>Nasazení</th>
                            <th>Umístění</th>
                            <th>Akce</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            data.forEach(participant => {
                html += `
                    <tr>
                        <td>${participant.name}</td>
                        <td>${participant.seed || '-'}</td>
                        <td>${participant.final_rank || '-'}</td>
                        <td><a href="hrac.html?id=${participant.id}" class="btn">Detail</a></td>
                    </tr>
                `;
            });
            
            html += `
                    </tbody>
                </table>
            `;
            
            participantsContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba při načítání účastníků turnaje:', error);
            participantsContainer.innerHTML = `<p>Nepodařilo se načíst účastníky turnaje: ${error.message}</p>`;
        });
}

/**
 * Načte zápasy turnaje z API
 * 
 * @param {number} tournamentId ID turnaje
 */
function fetchTournamentMatches(tournamentId) {
    const matchesContainer = document.getElementById('matches-content');
    
    fetch(`php/get-tournament-matches.php?tournament_id=${tournamentId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se načíst zápasy turnaje');
            }
            return response.json();
        })
        .then(data => {
            if (!data || data.length === 0) {
                matchesContainer.innerHTML = '<p>Žádné zápasy k zobrazení</p>';
                return;
            }

            // Seskupení zápasů podle kol
            const rounds = {};
            data.forEach(match => {
                if (!rounds[match.round]) {
                    rounds[match.round] = [];
                }
                rounds[match.round].push(match);
            });
            
            // Zobrazení zápasů turnaje po kolech
            let html = '';
            
            Object.keys(rounds).sort((a, b) => a - b).forEach(round => {
                html += `
                    <div class="round">
                        <h4>Kolo ${round}</h4>
                        <table>
                            <thead>
                                <tr>
                                    <th>Hráč 1</th>
                                    <th>Hráč 2</th>
                                    <th>Výsledek</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                rounds[round].forEach(match => {
                    const player1Name = match.player1_name || 'TBD';
                    const player2Name = match.player2_name || 'TBD';
                    
                    let result = 'Neurčeno';
                    if (match.score_player1 !== null && match.score_player2 !== null) {
                        result = `${match.score_player1}:${match.score_player2}`;
                    }
                    
                    html += `
                        <tr>
                            <td>${match.player1_id ? `<a href="hrac.html?id=${match.player1_id}">${player1Name}</a>` : player1Name}</td>
                            <td>${match.player2_id ? `<a href="hrac.html?id=${match.player2_id}">${player2Name}</a>` : player2Name}</td>
                            <td>${result}</td>
                        </tr>
                    `;
                });
                
                html += `
                            </tbody>
                        </table>
                    </div>
                `;
            });
            
            matchesContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba při načítání zápasů turnaje:', error);
            matchesContainer.innerHTML = `<p>Nepodařilo se načíst zápasy turnaje: ${error.message}</p>`;
        });
}

/**
 * Převede status turnaje na čitelný text
 * 
 * @param {string} status Status turnaje
 * @return {string} Čitelný text statusu
 */
function getStatusText(status) {
    switch (status) {
        case 'pending':
            return 'Čeká na zahájení';
        case 'underway':
            return 'Probíhá';
        case 'complete':
            return 'Dokončeno';
        default:
            return 'Neurčeno';
    }
}
