/* <PERSON><PERSON><PERSON><PERSON><PERSON> reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #ffffff;
    background-color: #000000;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* <PERSON><PERSON><PERSON><PERSON><PERSON> */
header {
    background-color: #dc143c;
    color: #ffffff;
    padding: 20px 0;
}

header h1 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 20px;
}

/* Menu */
nav ul {
    display: flex;
    justify-content: center;
    list-style: none;
    flex-wrap: wrap;
}

nav li {
    margin: 0 10px;
}

nav a {
    color: #ffffff;
    text-decoration: none;
    padding: 10px 15px;
    display: block;
    border-radius: 5px;
    transition: background-color 0.3s;
}

nav a:hover,
nav a.active {
    background-color: #06cef9;
}

/* Obsah */
main {
    padding: 40px 0;
}

section {
    margin-bottom: 40px;
    background-color: #1a1a1a;
    padding: 30px;
    border-radius: 8px;
}

h2 {
    color: #dc143c;
    margin-bottom: 20px;
    border-bottom: 2px solid #dc143c;
    padding-bottom: 10px;
}




/* Tabulky */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

th, td {
    padding: 12px;
    text-align: center;
    border-bottom: 1px solid #444;
}

th {
    background-color: #dc143c;
    color: #ffffff;
}

tr:hover {
    background-color: #333;
}

/* Tlačítka */
.btn {
    background-color: #06cef9;
    color: #ffffff;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    display: inline-block;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #0056b3;
}

/* Footer */
footer {
    background-color: #dc143c;
    color: #ffffff;
    text-align: center;
    padding: 20px 0;
    margin-top: 40px;
}

/* Responzivní design */
@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
    }

    nav li {
        margin: 5px 0;
    }

    header h1 {
        font-size: 1.5rem;
    }
}
.stats {
    background-color: #1a1a1a; /* Tmavě šedé pozadí */
    color: #ffffff;
    padding: 2rem;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(255, 255, 255, 0.1);
    border: 1px solid #333;
}

/* Tournaments section */
.tournaments {
    background-color: #1a1a1a; /* Tmavě šedé pozadí */
    color: #ffffff;
    padding: 2rem;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(255, 255, 255, 0.1);
    border: 1px solid #333;
}

/* Tables */
table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    background-color: #1a1a1a;
    color: #ffffff;
}

table th, table td {
    padding: 0.75rem;
    text-align: center;
    border-bottom: 1px solid #444;
}

table th {
    background-color: #dc143c; /* Červené hlavičky */
    color: #ffffff;
    font-weight: bold;
}

table tr:hover {
    background-color: #333;
}

/* Footer */
footer {
    background-color: #dc143c; /* Červené pozadí */
    color: #fff;
    padding: 1.5rem 0;
    text-align: center;
}

/* Responsive */
@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
    }

    nav ul li {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }

    .hero h2 {
        font-size: 2rem;
    }
}

/* Buttons */
.btn {
    display: inline-block;
    background-color: #3498db;
    color: #fff;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    font-size: 1rem;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #95a5a6;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

/* Forms */
form {
    margin-top: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

input[type="text"],
input[type="email"],
input[type="password"],
select,
textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

input[type="submit"] {
    background-color: #3498db;
    color: #fff;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
}

input[type="submit"]:hover {
    background-color: #2980b9;
}

/* Homepage specific styles */
.schedule-and-events {
    background-color: #111111;
    padding: 2rem 0;
    border-bottom: 3px solid #dc143c;
}

.schedule-events-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    align-items: start;
}

.round-schedule-section {
    background-color: #1a1a1a;
    color: #ffffff;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(255, 255, 255, 0.1);
    border: 1px solid #333;
}

.round-schedule-section h2 {
    color: #ffffff;
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    border-bottom: 3px solid #dc143c;
    padding-bottom: 0.5rem;
}

.events-section {
    background-color: #1a1a1a;
    color: #ffffff;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(255, 255, 255, 0.1);
    border: 1px solid #333;
}

.events-section h2 {
    color: #ffffff;
    margin-bottom: 1rem;
    font-size: 1.4rem;
    border-bottom: 2px solid #dc143c;
    padding-bottom: 0.5rem;
}

.events-section h3 {
    color: #ffffff;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    border-bottom: 2px solid #dc143c;
    padding-bottom: 0.5rem;
}

.news-content {
    padding: 2rem 0;
}

.news-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: start;
}

.world-news-section,
.czech-news-section {
    background-color: #1a1a1a;
    color: #ffffff;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(255, 255, 255, 0.1);
    border: 1px solid #333;
}

.world-news-section h2,
.czech-news-section h2 {
    color: #ffffff;
    margin-bottom: 1.5rem;
    font-size: 1.6rem;
    border-bottom: 3px solid #dc143c;
    padding-bottom: 0.5rem;
}

/* Tournament Countdowns */
.tournament-countdowns {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: #fff;
    padding: 3rem 0;
    margin-top: 2rem;
}

.tournament-countdowns h2 {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2rem;
    color: #fff;
}

.countdown-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.countdown-card {
    background-color: #fff;
    color: #2c3e50;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.countdown-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3498db, #2980b9);
}

.countdown-card.blue-oyster::before {
    background: linear-gradient(90deg, #3498db, #2980b9);
}

.countdown-card.rosti::before {
    background: linear-gradient(90deg, #e67e22, #d35400);
}

.countdown-card.sob::before {
    background: linear-gradient(90deg, #9b59b6, #8e44ad);
}

.countdown-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.countdown-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.countdown-card h3 {
    margin-bottom: 1rem;
    font-size: 1.4rem;
    color: #2c3e50;
}

.countdown-days {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #e74c3c;
}

.countdown-label {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.countdown-info {
    color: #95a5a6;
    font-size: 0.8rem;
    font-style: italic;
}



/* News items */
.news-item {
    border-bottom: 1px solid #ecf0f1;
    padding: 1rem 0;
    margin-bottom: 1rem;
}

.news-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.news-item h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.news-item p {
    color: #7f8c8d;
    font-size: 0.9rem;
    line-height: 1.5;
}

.news-date {
    color: #95a5a6;
    font-size: 0.8rem;
    margin-top: 0.5rem;
}

/* Event items */
.event-item {
    display: flex;
    align-items: center;
    padding: 0.8rem 0;
    border-bottom: 1px solid #ecf0f1;
}

.event-item:last-child {
    border-bottom: none;
}

.event-date {
    background-color: #3498db;
    color: #fff;
    padding: 0.5rem;
    border-radius: 5px;
    text-align: center;
    margin-right: 1rem;
    min-width: 60px;
}

.event-date .day {
    font-size: 1.2rem;
    font-weight: 700;
    display: block;
}

.event-date .month {
    font-size: 0.8rem;
    text-transform: uppercase;
}

.event-info h5 {
    color: #2c3e50;
    margin-bottom: 0.2rem;
    font-size: 0.95rem;
}

.event-info p {
    color: #7f8c8d;
    font-size: 0.8rem;
    margin: 0;
}

/* Responsive design for homepage */
@media (max-width: 1024px) {
    .schedule-events-layout {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .news-layout {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .countdown-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .schedule-events-layout {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .news-layout {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .hero h2 {
        font-size: 2.2rem;
    }

    .countdown-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .countdown-card {
        padding: 1.5rem;
    }

    .countdown-days {
        font-size: 2.5rem;
    }
}

/* Round Schedule Styles */
.matches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.match-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #3498db;
}

.match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #ecf0f1;
}

.match-round {
    font-weight: bold;
    color: #3498db;
    font-size: 0.9rem;
}

.match-time {
    color: #7f8c8d;
    font-size: 0.8rem;
}

.match-teams {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.team {
    flex: 1;
    text-align: center;
}

.team-name {
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.2rem;
}

.team-players {
    font-size: 0.8rem;
    color: #7f8c8d;
}

.vs-separator {
    margin: 0 1rem;
    font-weight: bold;
    color: #e74c3c;
    font-size: 1.2rem;
}

/* Special Tournaments */
.special-tournaments {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #ecf0f1;
}

.special-tournaments h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    border-bottom: 2px solid #e74c3c;
    padding-bottom: 0.5rem;
}

.special-tournament-item {
    display: flex;
    align-items: center;
    padding: 0.8rem 0;
    border-bottom: 1px solid #ecf0f1;
}

.special-tournament-item:last-child {
    border-bottom: none;
}

.special-tournament-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
}

.special-tournament-info h5 {
    color: #2c3e50;
    margin-bottom: 0.2rem;
    font-size: 0.95rem;
}

.special-tournament-info p {
    color: #7f8c8d;
    font-size: 0.8rem;
    margin: 0;
}

/* Mini Countdowns */
.tournament-countdowns-mini {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #ecf0f1;
}

.tournament-countdowns-mini h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.countdown-mini-grid {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.countdown-mini {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem;
    background-color: #f8f9fa;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid #3498db;
}

.countdown-mini.blue-oyster {
    border-left-color: #3498db;
}

.countdown-mini.rosti {
    border-left-color: #e67e22;
}

.countdown-mini.sob {
    border-left-color: #9b59b6;
}

.countdown-mini:hover {
    background-color: #ecf0f1;
    transform: translateX(5px);
}

.countdown-mini-name {
    font-weight: bold;
    color: #2c3e50;
    font-size: 0.9rem;
}

.countdown-mini-days {
    font-weight: bold;
    color: #e74c3c;
    font-size: 1.2rem;
}

.countdown-mini-info {
    color: #7f8c8d;
    font-size: 0.8rem;
}

/* Responsive adjustments for new layout */
@media (max-width: 768px) {
    .matches-grid {
        grid-template-columns: 1fr;
    }

    .match-teams {
        flex-direction: column;
        gap: 0.5rem;
    }

    .vs-separator {
        margin: 0.5rem 0;
    }

    .countdown-mini {
        padding: 0.6rem;
    }

    .countdown-mini-name {
        font-size: 0.8rem;
    }

    .countdown-mini-days {
        font-size: 1rem;
    }
}
