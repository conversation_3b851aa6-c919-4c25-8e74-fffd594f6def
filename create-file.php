<?php
/**
 * Jednoduchý skript pro vytvoření souboru na serveru
 * Nahrajte tento soubor a pak ho spusťte
 */

// Kontrola, zda je požadavek POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $filename = $_POST['filename'] ?? '';
    $content = $_POST['content'] ?? '';
    
    if (empty($filename) || empty($content)) {
        die('Chybí název souboru nebo obsah');
    }
    
    // Bezpečnostní kontrola
    if (!preg_match('/^[a-zA-Z0-9_-]+\.php$/', $filename)) {
        die('Neplatný název souboru');
    }
    
    $filepath = '/var/www/html/php/' . $filename;
    
    if (file_put_contents($filepath, $content)) {
        chmod($filepath, 0644);
        echo "Soubor {$filename} byl úspěšně vytvořen!";
    } else {
        echo "Chyba při vytváření souboru {$filename}";
    }
    exit;
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Vytvoření souboru</title>
</head>
<body>
    <h1>Vytvoření PHP souboru</h1>
    <form method="POST">
        <p>
            <label>Název souboru:</label><br>
            <input type="text" name="filename" placeholder="nazev-souboru.php" required>
        </p>
        <p>
            <label>Obsah souboru:</label><br>
            <textarea name="content" rows="20" cols="80" required></textarea>
        </p>
        <p>
            <button type="submit">Vytvořit soubor</button>
        </p>
    </form>
</body>
</html>
