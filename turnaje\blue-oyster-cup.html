<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blue Oyster Cup - Bruntálská šipková liga</title>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Sloučený červený banner s uvítáním a menu -->
    <header class="unified-header">
        <div class="container">
            <!-- Hlavní nadpis -->
            <div class="welcome-content">
                <h1>Bruntálská šipková liga</h1>
            </div>

            <!-- Menu -->
            <nav>
                <ul>
                    <li><a href="../index.html" data-color="home">Dom<PERSON></a></li>
                    <li><a href="../hraci.html" data-color="players">Hr<PERSON><PERSON>i</a></li>
                    <li class="dropdown">
                        <a href="../liga.html" data-color="league">Liga</a>
                        <ul class="dropdown-menu">
                            <li><a href="../liga.html#tabulka">Tabulka ligy</a></li>
                            <li><a href="../liga.html#rozpis">Rozpis zápasů</a></li>
                            <li><a href="../liga.html#pravidla">Pravidla</a></li>
                        </ul>
                    </li>
                    <li><a href="blue-oyster-cup.html" class="active" data-color="blue-oyster">Blue Oyster Cup</a></li>
                    <li><a href="rosti-cup.html" data-color="rosti">Roští Cup</a></li>
                    <li><a href="sob-cup.html" data-color="sob">Sob Cup</a></li>
                    <li><a href="specialni.html" data-color="special">Speciální turnaje</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <section class="tournament-category">
            <div class="container">
                <h2>Blue Oyster Cup</h2>
                <p class="category-description">Turnaje série Blue Oyster Cup</p>

                <div id="category-tournaments">
                    <p>Načítání turnajů...</p>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2025 Bruntálská šipková liga</p>
        </div>
    </footer>

    <script src="../js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Načtení turnajů kategorie
            fetchCategoryTournaments(1); // ID kategorie Blue Oyster Cup
        });

        /**
         * Načte turnaje kategorie z API
         *
         * @param {number} categoryId ID kategorie
         */
        function fetchCategoryTournaments(categoryId) {
            const tournamentsContainer = document.getElementById('category-tournaments');

            fetch('../php/get-tournaments.php')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Nepodařilo se načíst turnaje');
                    }
                    return response.json();
                })
                .then(data => {
                    // Filtrování turnajů podle kategorie
                    const categoryTournaments = data.filter(tournament => tournament.category_id == categoryId);

                    if (categoryTournaments.length === 0) {
                        tournamentsContainer.innerHTML = '<p>Žádné turnaje v této kategorii</p>';
                        return;
                    }

                    // Seřazení turnajů podle data (nejnovější první)
                    categoryTournaments.sort((a, b) => new Date(b.start_date) - new Date(a.start_date));

                    // Zobrazení turnajů kategorie
                    let html = `
                        <table>
                            <thead>
                                <tr>
                                    <th>Název</th>
                                    <th>Datum</th>
                                    <th>Status</th>
                                    <th>Akce</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;

                    categoryTournaments.forEach(tournament => {
                        const startDate = tournament.start_date
                            ? new Date(tournament.start_date).toLocaleDateString('cs-CZ')
                            : 'Neurčeno';
                        const status = getStatusText(tournament.status);

                        html += `
                            <tr>
                                <td>${tournament.name}</td>
                                <td>${startDate}</td>
                                <td>${status}</td>
                                <td><a href="../turnaj.html?id=${tournament.id}" class="btn">Detail</a></td>
                            </tr>
                        `;
                    });

                    html += `
                            </tbody>
                        </table>
                    `;

                    tournamentsContainer.innerHTML = html;
                })
                .catch(error => {
                    console.error('Chyba při načítání turnajů:', error);
                    tournamentsContainer.innerHTML = `<p>Nepodařilo se načíst turnaje: ${error.message}</p>`;
                });
        }

        /**
         * Převede status turnaje na čitelný text
         *
         * @param {string} status Status turnaje
         * @return {string} Čitelný text statusu
         */
        function getStatusText(status) {
            switch (status) {
                case 'pending':
                    return 'Čeká na zahájení';
                case 'underway':
                    return 'Probíhá';
                case 'complete':
                    return 'Dokončeno';
                default:
                    return 'Neurčeno';
            }
        }
    </script>
</body>
</html>
