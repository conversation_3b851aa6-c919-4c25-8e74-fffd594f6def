<?php
/**
 * Skript pro získání seznamu turnajů z Challonge API
 */

// Připojení k Challonge API
require_once 'challonge-api.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

try {
    // Získání seznamu turnajů z Challonge API
    $tournaments = $challongeApi->getTournaments();
    
    // Vrácení seznamu turnajů jako JSON
    echo json_encode($tournaments);
} catch (Exception $e) {
    // Vrácení chybové zprávy jako JSON
    http_response_code(500);
    echo json_encode(['error' => 'Nepodařilo se získat seznam turnajů z Challonge API: ' . $e->getMessage()]);
}
