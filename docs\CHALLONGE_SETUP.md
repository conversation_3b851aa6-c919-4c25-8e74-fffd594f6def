# Challonge API Integrace - Návod k nastavení

## <PERSON><PERSON><PERSON>led

Tato integrace umožňuje automatickou synchronizaci turnajů z Challonge.com s webovou aplikací Bruntálské šipkové ligy. Systém automaticky stahuje turnaje, kategorizuje je a přiděluje body hráčům podle jejich umístění.

## Bodovací systém

- **1. místo**: 100 bodů
- **2. místo**: 90 bodů
- **3. místo**: 80 bodů
- **4. místo**: 70 bodů
- **5.-6. místo**: 60 bodů
- **7.-8. místo**: 50 bodů
- **9.-12. místo**: 40 bodů
- **13.-17. místo**: 30 bodů
- **18.-25. místo**: 20 bodů
- **26.-32. místo**: 10 bodů
- **33+ místo**: 0 bodů

## <PERSON><PERSON><PERSON><PERSON> turnajů

Turnaje se automaticky kategorizuj<PERSON> podle názvu:

- **Blue Oyster Cup**: turnaje obsahujíc<PERSON> "blue oyster" nebo "boc"
- **Sob Cup**: turnaje obsahující "sob cup" nebo "sob"
- **Roští Cup**: turnaje obsahující "roští" nebo "rosti"
- **Speciální turnaje**: všechny ostatní turnaje

## Nastavení

### 1. Přihlašovací údaje

V admin rozhraní (`/admin/challonge.html`) nastavte:
- **API klíč**: `adbPU7e6QlOzIvxzSDnohavu61YwTNhl6FIDkF3H`
- **Username**: `Rezexil`

### 2. Databázové tabulky

Ujistěte se, že existují následující tabulky:

```sql
-- Kategorie turnajů
CREATE TABLE tournament_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Turnaje
CREATE TABLE tournaments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    challonge_id VARCHAR(50) UNIQUE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    start_date DATETIME,
    category_id INT,
    status VARCHAR(50),
    participants_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES tournament_categories(id)
);

-- Účastníci turnajů
CREATE TABLE tournament_participants (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tournament_id INT,
    challonge_participant_id VARCHAR(50),
    name VARCHAR(255) NOT NULL,
    final_rank INT,
    points INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tournament_id) REFERENCES tournaments(id),
    UNIQUE KEY unique_participant (tournament_id, challonge_participant_id)
);

-- Nastavení
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3. Základní kategorie

Vložte základní kategorie:

```sql
INSERT INTO tournament_categories (name, description) VALUES
('Blue Oyster Cup', 'Úterní turnaje Blue Oyster Cup'),
('Sob Cup', 'Čtvrteční turnaje Sob Cup'),
('Roští Cup', 'Střední turnaje Roští Cup'),
('Speciální turnaje', 'Ostatní a speciální turnaje');
```

## Použití

### 1. Testování systému

Před první synchronizací spusťte test:
1. Přihlaste se do admin rozhraní
2. Jděte na stránku Challonge
3. Klikněte na "Otestovat systém"
4. Zkontrolujte výsledky testů

### 2. Manuální synchronizace

Pro okamžitou synchronizaci:
1. V admin rozhraní klikněte na "Kompletní synchronizace"
2. Sledujte průběh a výsledky
3. Zkontrolujte statistiky

### 3. Automatická synchronizace

Pro automatické spouštění každé pondělí:

```bash
# Přidejte do crontab (crontab -e)
0 6 * * 1 /usr/bin/php /path/to/php/weekly-sync-cron.php

# Nebo pro testování každou hodinu:
0 * * * * /usr/bin/php /path/to/php/weekly-sync-cron.php
```

### 4. Sledování logů

Logy se ukládají do `logs/weekly-sync.log`:

```bash
tail -f logs/weekly-sync.log
```

## API Endpointy

### Testování
- `GET /php/test-challonge-sync.php` - Test systému

### Synchronizace
- `POST /php/sync-and-evaluate-tournaments.php` - Kompletní synchronizace

### Statistiky
- `GET /php/get-challonge-stats.php?category=all` - Všechny statistiky
- `GET /php/get-challonge-stats.php?category=Blue%20Oyster%20Cup` - Statistiky BOC

## Řešení problémů

### Chyba připojení k API
1. Zkontrolujte API klíč v nastavení
2. Ověřte internetové připojení
3. Zkontrolujte, zda je Challonge.com dostupné

### Chybějící kategorie
1. Zkontrolujte tabulku `tournament_categories`
2. Přidejte chybějící kategorie pomocí SQL

### Nesprávná kategorizace
1. Zkontrolujte názvy turnajů v Challonge
2. Upravte logiku kategorizace v `challonge-api.php`
3. Ručně přeřaďte turnaje v admin rozhraní

### Chybějící body
1. Zkontrolujte, zda je turnaj označen jako "complete"
2. Ověřte, zda účastníci mají nastavené `final_rank`
3. Spusťte synchronizaci znovu

## Monitoring

### Kontrolní body
- Poslední synchronizace: zobrazeno v admin rozhraní
- Počet zpracovaných turnajů
- Počet udělených bodů
- Turnaje vyžadující kontrolu kategorie

### Upozornění
Systém upozorní na:
- Turnaje s nejasnou kategorií
- Chyby při zpracování
- Neúspěšné API volání

## Bezpečnost

- API klíč je uložen v databázi (šifrovaně)
- Admin rozhraní vyžaduje přihlášení
- Logy neobsahují citlivé údaje
- Cron skripty běží s omezenými právy
