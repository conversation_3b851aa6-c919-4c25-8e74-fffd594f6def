document.addEventListener('DOMContentLoaded', function() {
    // Načtení seznamu turnajů
    if (document.getElementById('tournaments-list')) {
        fetchTournaments();
    }
});

/**
 * Načte seznam turnajů z API
 */
function fetchTournaments() {
    const tournamentsContainer = document.getElementById('tournaments-list');
    
    fetch('php/get-tournaments.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se načíst seznam turnajů');
            }
            return response.json();
        })
        .then(data => {
            if (data.length === 0) {
                tournamentsContainer.innerHTML = '<p>Žádné turnaje k zobrazení</p>';
                return;
            }

            // Seřazení turnajů podle data (nejnovější první)
            data.sort((a, b) => new Date(b.start_date) - new Date(a.start_date));

            // Vytvoření seznamu turnajů
            let html = `
                <table>
                    <thead>
                        <tr>
                            <th><PERSON><PERSON><PERSON>v</th>
                            <th><PERSON><PERSON><PERSON></th>
                            <th>Datum</th>
                            <th>Status</th>
                            <th>Akce</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            data.forEach(tournament => {
                const startDate = tournament.start_date ? new Date(tournament.start_date).toLocaleDateString('cs-CZ') : 'Neurčeno';
                const category = tournament.category_name || 'Nezařazeno';
                const status = getStatusText(tournament.status);
                
                html += `
                    <tr>
                        <td>${tournament.name}</td>
                        <td>${category}</td>
                        <td>${startDate}</td>
                        <td>${status}</td>
                        <td><a href="turnaj.html?id=${tournament.id}" class="btn">Detail</a></td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                </table>
            `;

            tournamentsContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba při načítání seznamu turnajů:', error);
            tournamentsContainer.innerHTML = `<p>Nepodařilo se načíst seznam turnajů: ${error.message}</p>`;
        });
}

/**
 * Převede status turnaje na čitelný text
 * 
 * @param {string} status Status turnaje
 * @return {string} Čitelný text statusu
 */
function getStatusText(status) {
    switch (status) {
        case 'pending':
            return 'Čeká na zahájení';
        case 'underway':
            return 'Probíhá';
        case 'complete':
            return 'Dokončeno';
        default:
            return 'Neurčeno';
    }
}
