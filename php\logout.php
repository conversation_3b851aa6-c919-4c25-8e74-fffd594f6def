<?php
/**
 * Skript pro odhlášení
 */

// Spuštění session
session_start();

// Nastaven<PERSON> hlav<PERSON> pro JSON odpověď
header('Content-Type: application/json');

// Odstranění všech session proměnných
$_SESSION = [];

// Zničení session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(
        session_name(),
        '',
        time() - 42000,
        $params["path"],
        $params["domain"],
        $params["secure"],
        $params["httponly"]
    );
}

// Zničení session
session_destroy();

// Vrácení úspěšné odpovědi
echo json_encode(['success' => true, 'message' => 'Odhlášení úspěšné']);
