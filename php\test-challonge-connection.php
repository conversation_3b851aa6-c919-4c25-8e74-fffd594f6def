<?php
/**
 * Skript pro testování připojení k Challonge API
 */

// Spuštění session
session_start();

// Kontrola, zda je uživatel přihlášen
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('HTTP/1.1 401 Unauthorized');
    echo json_encode(['success' => false, 'message' => 'Nejste přihlášen']);
    exit;
}

// Připojení k Challonge API
require_once 'challonge-api.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

try {
    // Testování připojení k Challonge API
    $tournaments = $challongeApi->getTournaments();
    
    // Vrácení úspěšné odpovědi
    echo json_encode([
        'success' => true,
        'message' => 'Připojení k Challonge API je funkční',
        'tournaments_count' => count($tournaments)
    ]);
} catch (Exception $e) {
    // Vrácení chybové zprávy jako JSON
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Chyba při připojení k Challonge API: ' . $e->getMessage()]);
}
