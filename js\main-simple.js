// Jednoduchý JavaScript pro Bruntálskou šipkovou ligu

document.addEventListener('DOMContentLoaded', function() {
    console.log('Bruntálská šipková liga načtena');
    
    // Načtení dat
    loadUpcomingTournaments();
    loadRecentResults();
});

/**
 * Načte nadcházejí<PERSON><PERSON> turnaje
 */
function loadUpcomingTournaments() {
    const container = document.getElementById('upcoming-tournaments');
    if (!container) return;
    
    // Ukázková data
    const tournaments = [
        {
            name: 'Blue Oyster Cup #15',
            date: '2025-01-28',
            type: 'Blue Oyster Cup'
        },
        {
            name: 'Roští Cup #12',
            date: '2025-01-29',
            type: 'Roští Cup'
        },
        {
            name: 'Sob Cup #8',
            date: '2025-01-30',
            type: 'Sob Cup'
        }
    ];
    
    let html = '<div class="tournaments-grid">';
    tournaments.forEach(tournament => {
        const date = new Date(tournament.date).toLocaleDateString('cs-CZ');
        html += `
            <div class="tournament-card">
                <h3>${tournament.name}</h3>
                <p><strong>Datum:</strong> ${date}</p>
                <p><strong>Typ:</strong> ${tournament.type}</p>
                <a href="#" class="btn">Registrovat se</a>
            </div>
        `;
    });
    html += '</div>';
    
    container.innerHTML = html;
}

/**
 * Načte aktuální výsledky
 */
function loadRecentResults() {
    const container = document.getElementById('recent-results');
    if (!container) return;
    
    // Ukázková data
    const results = [
        {
            tournament: 'Blue Oyster Cup #14',
            winner: 'Jan Novák',
            date: '2025-01-21'
        },
        {
            tournament: 'Roští Cup #11',
            winner: 'Petr Svoboda',
            date: '2025-01-22'
        },
        {
            tournament: 'Sob Cup #7',
            winner: 'Pavel Dvořák',
            date: '2025-01-23'
        }
    ];
    
    let html = '<div class="results-list">';
    results.forEach(result => {
        const date = new Date(result.date).toLocaleDateString('cs-CZ');
        html += `
            <div class="result-item">
                <h4>${result.tournament}</h4>
                <p><strong>Vítěz:</strong> ${result.winner}</p>
                <p><strong>Datum:</strong> ${date}</p>
            </div>
        `;
    });
    html += '</div>';
    
    container.innerHTML = html;
}

/**
 * Zvýrazní aktivní menu položku
 */
function setActiveMenuItem() {
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const menuLinks = document.querySelectorAll('nav a');
    
    menuLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPage) {
            link.classList.add('active');
        }
    });
}
