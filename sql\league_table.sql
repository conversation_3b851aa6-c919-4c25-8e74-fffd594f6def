-- Tabulka pro ligové statistiky hrá<PERSON>ů
CREATE TABLE IF NOT EXISTS league_standings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    player_id INT NOT NULL,
    season VARCHAR(20) NOT NULL DEFAULT '2024-25',
    
    -- <PERSON><PERSON><PERSON><PERSON><PERSON> statistiky
    games_played INT DEFAULT 0,
    wins INT DEFAULT 0,
    losses INT DEFAULT 0,
    draws INT DEFAULT 0,
    
    -- Body
    points INT DEFAULT 0,
    
    -- <PERSON><PERSON>n<PERSON> statistiky
    legs_won INT DEFAULT 0,
    legs_lost INT DEFAULT 0,
    sets_won INT DEFAULT 0,
    sets_lost INT DEFAULT 0,
    
    -- Průměry
    average_score DECIMAL(5,2) DEFAULT 0.00,
    highest_finish INT DEFAULT 0,
    
    -- Speci<PERSON><PERSON><PERSON> statistiky
    checkout_180 INT DEFAULT 0,
    checkout_140_plus INT DEFAULT 0,
    checkout_100_plus INT DEFAULT 0,
    
    -- Pozice v tabulce
    position INT DEFAULT 0,
    
    -- <PERSON><PERSON><PERSON><PERSON>
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (player_id) REFERENCES players(id) ON DELETE CASCADE,
    UNIQUE KEY unique_player_season (player_id, season)
);

-- Vložení ukázkových dat na základě typické šipkové ligy
INSERT INTO league_standings (player_id, season, games_played, wins, losses, draws, points, legs_won, legs_lost, sets_won, sets_lost, average_score, highest_finish, checkout_180, checkout_140_plus, checkout_100_plus, position) VALUES
-- Nejdříve musíme vložit hráče
-- Toto je jen ukázka struktury - skutečná data budou z PDF

-- Ukázkový hráč 1
(1, '2024-25', 26, 20, 4, 2, 42, 156, 98, 78, 39, 85.50, 170, 12, 28, 45, 1),
-- Ukázkový hráč 2  
(2, '2024-25', 26, 18, 6, 2, 38, 142, 112, 71, 46, 82.30, 164, 8, 24, 41, 2),
-- Ukázkový hráč 3
(3, '2024-25', 26, 16, 8, 2, 34, 138, 116, 69, 48, 79.80, 158, 6, 22, 38, 3);

-- Vložení ukázkových hráčů (pokud neexistují)
INSERT IGNORE INTO players (id, name, email) VALUES
(1, 'Jan Novák', '<EMAIL>'),
(2, 'Petr Svoboda', '<EMAIL>'),
(3, 'Pavel Dvořák', '<EMAIL>'),
(4, 'Tomáš Černý', '<EMAIL>'),
(5, 'Martin Procházka', '<EMAIL>'),
(6, 'Jiří Novotný', '<EMAIL>'),
(7, 'Václav Veselý', '<EMAIL>'),
(8, 'Michal Horák', '<EMAIL>'),
(9, 'Lukáš Krejčí', '<EMAIL>'),
(10, 'David Fiala', '<EMAIL>'),
(11, 'Radek Mareš', '<EMAIL>'),
(12, 'Ondřej Pokorný', '<EMAIL>'),
(13, 'Jakub Pešek', '<EMAIL>'),
(14, 'Filip Růžička', '<EMAIL>'),
(15, 'Marek Štěpánek', '<EMAIL>'),
(16, 'Roman Beneš', '<EMAIL>');
