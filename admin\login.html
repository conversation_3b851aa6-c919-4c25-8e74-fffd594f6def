<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Přihlášení - Administrace Bruntálské šipkové ligy</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body class="admin-page login-page">
    <div class="login-container">
        <div class="login-box">
            <h1>Přihlášení do administrace</h1>
            <div id="login-message"></div>
            <form id="login-form">
                <div class="form-group">
                    <label for="username">Uživatelské jméno</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">He<PERSON><PERSON></label>
                    <input type="password" id="password" name="password" required>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">Přihlásit se</button>
                </div>
            </form>
            <p class="back-link">
                <a href="../index.html">Zpět na web</a>
            </p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('login-form');
            const loginMessage = document.getElementById('login-message');
            
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                // Odeslání přihlašovacích údajů na server
                fetch('../php/login.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Přesměrování na administrační rozhraní
                        window.location.href = 'index.html';
                    } else {
                        // Zobrazení chybové zprávy
                        loginMessage.innerHTML = `<div class="alert alert-error">${data.message}</div>`;
                    }
                })
                .catch(error => {
                    console.error('Chyba při přihlašování:', error);
                    loginMessage.innerHTML = '<div class="alert alert-error">Chyba při přihlašování. Zkuste to prosím znovu.</div>';
                });
            });
        });
    </script>
</body>
</html>
