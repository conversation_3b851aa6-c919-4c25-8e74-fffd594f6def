<?php
/**
 * Cron skript pro týdenní synchronizaci Challonge dat
 * Spouští se každé pondělí pro vyhodnocení turnajů z předchozího týdne
 * 
 * Přidejte do crontab:
 * 0 6 * * 1 /usr/bin/php /path/to/weekly-sync-cron.php
 * (každé pondělí v 6:00 ráno)
 */

// Nastavení pro CLI spuštění
if (php_sapi_name() !== 'cli') {
    die('Tento skript lze spustit pouze z příkazové řádky');
}

// Připojení k databázi a Challonge API
require_once __DIR__ . '/db-connection.php';
require_once __DIR__ . '/challonge-api.php';

// Logování
$logFile = __DIR__ . '/../logs/weekly-sync.log';
$logDir = dirname($logFile);

// Vytvoření adresáře pro logy pokud neexistuje
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

function writeLog($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
    echo "[$timestamp] $message\n";
}

writeLog("=== Spuštění týdenní synchronizace Challonge ===");

try {
    // Kontrola, zda už dnes neproběhla synchronizace
    $stmt = $pdo->query("SELECT setting_value FROM settings WHERE setting_key = 'last_sync_time'");
    $lastSync = $stmt->fetchColumn();
    
    if ($lastSync) {
        $lastSyncDate = new DateTime($lastSync);
        $today = new DateTime();
        
        if ($lastSyncDate->format('Y-m-d') === $today->format('Y-m-d')) {
            writeLog("Synchronizace už dnes proběhla, ukončuji.");
            exit(0);
        }
    }
    
    // Získání seznamu všech turnajů z Challonge API
    writeLog("Načítání turnajů z Challonge API...");
    $challongeTournaments = $challongeApi->getTournaments();
    
    if (empty($challongeTournaments)) {
        writeLog("Žádné turnaje k synchronizaci");
        exit(0);
    }
    
    writeLog("Nalezeno " . count($challongeTournaments) . " turnajů");
    
    // Statistiky synchronizace
    $stats = [
        'tournaments_processed' => 0,
        'tournaments_new' => 0,
        'tournaments_updated' => 0,
        'participants_processed' => 0,
        'points_awarded' => 0,
        'categories' => [
            'Blue Oyster Cup' => 0,
            'Sob Cup' => 0,
            'Roští Cup' => 0,
            'Speciální turnaje' => 0
        ],
        'uncertain_tournaments' => []
    ];

    // Získání ID kategorií z databáze
    $categoryIds = [];
    $stmt = $pdo->query("SELECT id, name FROM tournament_categories");
    while ($row = $stmt->fetch()) {
        $categoryIds[$row['name']] = $row['id'];
    }

    // Zpracování každého turnaje
    foreach ($challongeTournaments as $tournamentData) {
        $tournament = $tournamentData['tournament'];
        $stats['tournaments_processed']++;
        
        writeLog("Zpracovávám turnaj: " . $tournament['name']);
        
        // Kategorizace turnaje
        $categoryName = $challongeApi->categorizeTournament($tournament['name']);
        $categoryId = $categoryIds[$categoryName] ?? $categoryIds['Speciální turnaje'];
        
        // Pokud kategorie není jasná, přidáme do seznamu nejistých
        if ($categoryName === 'Speciální turnaje' && 
            !preg_match('/christmas|vánoční|speciální/i', $tournament['name'])) {
            $stats['uncertain_tournaments'][] = [
                'name' => $tournament['name'],
                'url' => $tournament['full_challonge_url'],
                'suggested_category' => 'Speciální turnaje'
            ];
            writeLog("UPOZORNĚNÍ: Nejistá kategorie pro turnaj: " . $tournament['name']);
        }
        
        $stats['categories'][$categoryName]++;
        
        // Kontrola, zda turnaj už existuje v databázi
        $stmt = $pdo->prepare("SELECT id FROM tournaments WHERE challonge_id = ?");
        $stmt->execute([$tournament['id']]);
        $existingTournament = $stmt->fetch();
        
        if ($existingTournament) {
            // Aktualizace existujícího turnaje
            $stmt = $pdo->prepare("
                UPDATE tournaments SET 
                    name = ?, 
                    description = ?, 
                    start_date = ?, 
                    category_id = ?,
                    status = ?,
                    participants_count = ?,
                    updated_at = NOW()
                WHERE challonge_id = ?
            ");
            $stmt->execute([
                $tournament['name'],
                $tournament['description'] ?? '',
                $tournament['started_at'] ?? $tournament['created_at'],
                $categoryId,
                $tournament['state'],
                $tournament['participants_count'],
                $tournament['id']
            ]);
            $tournamentId = $existingTournament['id'];
            $stats['tournaments_updated']++;
            writeLog("Aktualizován existující turnaj ID: $tournamentId");
        } else {
            // Vložení nového turnaje
            $stmt = $pdo->prepare("
                INSERT INTO tournaments (
                    challonge_id, name, description, start_date, category_id, 
                    status, participants_count, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            $stmt->execute([
                $tournament['id'],
                $tournament['name'],
                $tournament['description'] ?? '',
                $tournament['started_at'] ?? $tournament['created_at'],
                $categoryId,
                $tournament['state'],
                $tournament['participants_count']
            ]);
            $tournamentId = $pdo->lastInsertId();
            $stats['tournaments_new']++;
            writeLog("Přidán nový turnaj ID: $tournamentId");
        }
        
        // Pokud je turnaj dokončený, zpracujeme účastníky a body
        if ($tournament['state'] === 'complete') {
            writeLog("Zpracovávám výsledky dokončeného turnaje...");
            processTournamentResults($pdo, $challongeApi, $tournament['id'], $tournamentId, $categoryName, $stats);
        }
    }
    
    // Aktualizace času poslední synchronizace
    $stmt = $pdo->prepare("
        INSERT INTO settings (setting_key, setting_value, created_at, updated_at)
        VALUES ('last_sync_time', NOW(), NOW(), NOW())
        ON DUPLICATE KEY UPDATE setting_value = NOW(), updated_at = NOW()
    ");
    $stmt->execute();
    
    // Výsledky synchronizace
    writeLog("=== Synchronizace dokončena ===");
    writeLog("Zpracovaných turnajů: " . $stats['tournaments_processed']);
    writeLog("Nových turnajů: " . $stats['tournaments_new']);
    writeLog("Aktualizovaných turnajů: " . $stats['tournaments_updated']);
    writeLog("Zpracovaných hráčů: " . $stats['participants_processed']);
    writeLog("Udělených bodů: " . $stats['points_awarded']);
    
    writeLog("Turnaje podle kategorií:");
    foreach ($stats['categories'] as $category => $count) {
        writeLog("  $category: $count");
    }
    
    if (!empty($stats['uncertain_tournaments'])) {
        writeLog("UPOZORNĚNÍ: Turnaje vyžadující kontrolu kategorie:");
        foreach ($stats['uncertain_tournaments'] as $tournament) {
            writeLog("  - " . $tournament['name']);
        }
    }

} catch (Exception $e) {
    writeLog("CHYBA: " . $e->getMessage());
    exit(1);
}

/**
 * Zpracuje výsledky turnaje a přidělí body hráčům
 */
function processTournamentResults($pdo, $challongeApi, $challongeTournamentId, $tournamentId, $categoryName, &$stats) {
    try {
        writeLog("  Načítání účastníků turnaje...");
        
        // Získání účastníků turnaje
        $participants = $challongeApi->getParticipants($challongeTournamentId);
        
        writeLog("  Nalezeno " . count($participants) . " účastníků");
        
        foreach ($participants as $participantData) {
            $participant = $participantData['participant'];
            $stats['participants_processed']++;
            
            // Kontrola, zda účastník už existuje
            $stmt = $pdo->prepare("SELECT id FROM tournament_participants WHERE tournament_id = ? AND challonge_participant_id = ?");
            $stmt->execute([$tournamentId, $participant['id']]);
            $existingParticipant = $stmt->fetch();
            
            $finalRank = $participant['final_rank'] ?? 999;
            $points = $challongeApi->calculatePoints($finalRank);
            
            if ($existingParticipant) {
                // Aktualizace existujícího účastníka
                $stmt = $pdo->prepare("
                    UPDATE tournament_participants SET 
                        name = ?, 
                        final_rank = ?, 
                        points = ?,
                        updated_at = NOW()
                    WHERE tournament_id = ? AND challonge_participant_id = ?
                ");
                $stmt->execute([
                    $participant['name'],
                    $finalRank,
                    $points,
                    $tournamentId,
                    $participant['id']
                ]);
            } else {
                // Vložení nového účastníka
                $stmt = $pdo->prepare("
                    INSERT INTO tournament_participants (
                        tournament_id, challonge_participant_id, name, final_rank, points, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, NOW(), NOW())
                ");
                $stmt->execute([
                    $tournamentId,
                    $participant['id'],
                    $participant['name'],
                    $finalRank,
                    $points
                ]);
            }
            
            $stats['points_awarded'] += $points;
            
            if ($finalRank <= 3) {
                writeLog("    " . $participant['name'] . " - " . $finalRank . ". místo (" . $points . " bodů)");
            }
        }
        
    } catch (Exception $e) {
        writeLog("  CHYBA při zpracování výsledků turnaje {$challongeTournamentId}: " . $e->getMessage());
    }
}
?>
