# PowerShell skript pro nahrání souborů na server
Write-Host "Nahrávání souborů na server..." -ForegroundColor Green

# Nastavení kódování na UTF-8
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# Použití SCP pro nahrání souborů
try {
    # Zkusíme použít pscp (PuTTY)
    & pscp -i "c:\users\<USER>\iok.private.ppk" -r *.php *.html *.css *.js ubuntu@***************:/var/www/html/
    Write-Host "Soubory úspěšně nahrány!" -ForegroundColor Green
} catch {
    Write-Host "Chyba při nahrávání: $($_.Exception.Message)" -ForegroundColor Red
}

Read-Host "Stiskněte Enter pro pokračování..."
