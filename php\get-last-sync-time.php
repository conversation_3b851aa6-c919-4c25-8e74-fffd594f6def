<?php
/**
 * Skript pro získání času poslední synchronizace
 */

// Připojení k databázi
require_once 'db-connection.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

try {
    // Získání času poslední synchronizace
    $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'last_sync_time'");
    $stmt->execute();
    $result = $stmt->fetch();
    
    if ($result) {
        echo json_encode(['last_sync_time' => $result['setting_value']]);
    } else {
        echo json_encode(['last_sync_time' => null]);
    }
} catch (PDOException $e) {
    // Vrácení chybové zprávy jako JSON
    http_response_code(500);
    echo json_encode(['error' => 'Nepodařilo se získat čas poslední synchronizace: ' . $e->getMessage()]);
}
