<?php
/**
 * Skript pro kompletní synchronizaci a vyhodnocení turnajů z Challonge
 * Spouští se každé pondělí pro vyhodnocení turnajů z předchozího týdne
 */

// Připojení k databázi a Challonge API
require_once 'db-connection.php';
require_once 'challonge-api.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

// Spuštění session pro admin kontrolu
session_start();

// Kontrola, zda je uživatel přihlášen (pokud se spouští manuálně)
if (isset($_SESSION['logged_in']) && $_SESSION['logged_in'] !== true) {
    header('HTTP/1.1 401 Unauthorized');
    echo json_encode(['success' => false, 'message' => 'Nejste přihlášen']);
    exit;
}

try {
    // Získání seznamu všech turnajů z Challonge API
    $challongeTournaments = $challongeApi->getTournaments();
    
    if (empty($challongeTournaments)) {
        echo json_encode(['status' => 'success', 'message' => 'Žádné turnaje k synchronizaci']);
        exit;
    }

    // Statistiky synchronizace
    $stats = [
        'tournaments_processed' => 0,
        'tournaments_new' => 0,
        'tournaments_updated' => 0,
        'participants_processed' => 0,
        'points_awarded' => 0,
        'categories' => [
            'Blue Oyster Cup' => 0,
            'Sob Cup' => 0,
            'Roští Cup' => 0,
            'Speciální turnaje' => 0
        ],
        'uncertain_tournaments' => []
    ];

    // Získání ID kategorií z databáze
    $categoryIds = [];
    $stmt = $pdo->query("SELECT id, name FROM tournament_categories");
    while ($row = $stmt->fetch()) {
        $categoryIds[$row['name']] = $row['id'];
    }

    // Zpracování každého turnaje
    foreach ($challongeTournaments as $tournamentData) {
        $tournament = $tournamentData['tournament'];
        $stats['tournaments_processed']++;
        
        // Kategorizace turnaje
        $categoryName = $challongeApi->categorizeTournament($tournament['name']);
        $categoryId = $categoryIds[$categoryName] ?? $categoryIds['Speciální turnaje'];
        
        // Pokud kategorie není jasná, přidáme do seznamu nejistých
        if ($categoryName === 'Speciální turnaje' && 
            !preg_match('/christmas|vánoční|speciální/i', $tournament['name'])) {
            $stats['uncertain_tournaments'][] = [
                'name' => $tournament['name'],
                'url' => $tournament['full_challonge_url'],
                'suggested_category' => 'Speciální turnaje'
            ];
        }
        
        $stats['categories'][$categoryName]++;
        
        // Kontrola, zda turnaj už existuje v databázi
        $stmt = $pdo->prepare("SELECT id FROM tournaments WHERE challonge_id = ?");
        $stmt->execute([$tournament['id']]);
        $existingTournament = $stmt->fetch();
        
        if ($existingTournament) {
            // Aktualizace existujícího turnaje
            $stmt = $pdo->prepare("
                UPDATE tournaments SET 
                    name = ?, 
                    description = ?, 
                    start_date = ?, 
                    category_id = ?,
                    status = ?,
                    participants_count = ?,
                    updated_at = NOW()
                WHERE challonge_id = ?
            ");
            $stmt->execute([
                $tournament['name'],
                $tournament['description'] ?? '',
                $tournament['started_at'] ?? $tournament['created_at'],
                $categoryId,
                $tournament['state'],
                $tournament['participants_count'],
                $tournament['id']
            ]);
            $tournamentId = $existingTournament['id'];
            $stats['tournaments_updated']++;
        } else {
            // Vložení nového turnaje
            $stmt = $pdo->prepare("
                INSERT INTO tournaments (
                    challonge_id, name, description, start_date, category_id, 
                    status, participants_count, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            ");
            $stmt->execute([
                $tournament['id'],
                $tournament['name'],
                $tournament['description'] ?? '',
                $tournament['started_at'] ?? $tournament['created_at'],
                $categoryId,
                $tournament['state'],
                $tournament['participants_count']
            ]);
            $tournamentId = $pdo->lastInsertId();
            $stats['tournaments_new']++;
        }
        
        // Pokud je turnaj dokončený, zpracujeme účastníky a body
        if ($tournament['state'] === 'complete') {
            processTournamentResults($pdo, $challongeApi, $tournament['id'], $tournamentId, $categoryName, $stats);
        }
    }
    
    // Aktualizace času poslední synchronizace
    $stmt = $pdo->prepare("
        INSERT INTO settings (setting_key, setting_value, created_at, updated_at)
        VALUES ('last_sync_time', NOW(), NOW(), NOW())
        ON DUPLICATE KEY UPDATE setting_value = NOW(), updated_at = NOW()
    ");
    $stmt->execute();
    
    // Vrácení statistik
    echo json_encode([
        'status' => 'success',
        'message' => 'Synchronizace dokončena úspěšně',
        'stats' => $stats
    ]);

} catch (Exception $e) {
    // Vrácení chybové zprávy
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Chyba při synchronizaci: ' . $e->getMessage()
    ]);
}

/**
 * Zpracuje výsledky turnaje a přidělí body hráčům
 */
function processTournamentResults($pdo, $challongeApi, $challongeTournamentId, $tournamentId, $categoryName, &$stats) {
    try {
        // Získání účastníků turnaje
        $participants = $challongeApi->getParticipants($challongeTournamentId);
        
        foreach ($participants as $participantData) {
            $participant = $participantData['participant'];
            $stats['participants_processed']++;
            
            // Kontrola, zda účastník už existuje
            $stmt = $pdo->prepare("SELECT id FROM tournament_participants WHERE tournament_id = ? AND challonge_participant_id = ?");
            $stmt->execute([$tournamentId, $participant['id']]);
            $existingParticipant = $stmt->fetch();
            
            $finalRank = $participant['final_rank'] ?? 999;
            $points = $challongeApi->calculatePoints($finalRank);
            
            if ($existingParticipant) {
                // Aktualizace existujícího účastníka
                $stmt = $pdo->prepare("
                    UPDATE tournament_participants SET 
                        name = ?, 
                        final_rank = ?, 
                        points = ?,
                        updated_at = NOW()
                    WHERE tournament_id = ? AND challonge_participant_id = ?
                ");
                $stmt->execute([
                    $participant['name'],
                    $finalRank,
                    $points,
                    $tournamentId,
                    $participant['id']
                ]);
            } else {
                // Vložení nového účastníka
                $stmt = $pdo->prepare("
                    INSERT INTO tournament_participants (
                        tournament_id, challonge_participant_id, name, final_rank, points, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, NOW(), NOW())
                ");
                $stmt->execute([
                    $tournamentId,
                    $participant['id'],
                    $participant['name'],
                    $finalRank,
                    $points
                ]);
            }
            
            $stats['points_awarded'] += $points;
        }
        
    } catch (Exception $e) {
        error_log("Chyba při zpracování výsledků turnaje {$challongeTournamentId}: " . $e->getMessage());
    }
}
?>
