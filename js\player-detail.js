document.addEventListener('DOMContentLoaded', function() {
    // Získání ID hráče z URL parametru
    const urlParams = new URLSearchParams(window.location.search);
    const playerId = urlParams.get('id');
    
    if (!playerId) {
        // Pokud není zadáno ID hráče, přesměrujeme na seznam hráčů
        window.location.href = 'hraci.html';
        return;
    }
    
    // Načtení detailu hráče
    fetchPlayerDetail(playerId);
    
    // Načtení statistik hráče
    fetchPlayerStats(playerId);
    
    // Načtení turnajů hráče
    fetchPlayerTournaments(playerId);
    
    // Načtení zápasů hráče
    fetchPlayerMatches(playerId);
});

/**
 * Načte detail hráče z API
 * 
 * @param {number} playerId ID hráče
 */
function fetchPlayerDetail(playerId) {
    const playerInfoContainer = document.getElementById('player-info');
    
    fetch(`php/get-player-details.php?id=${playerId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se načíst detail hráče');
            }
            return response.json();
        })
        .then(data => {
            if (!data) {
                playerInfoContainer.innerHTML = '<p>Hráč nebyl nalezen</p>';
                return;
            }

            // Aktualizace titulku stránky
            document.title = `${data.name} - Bruntálská šipková liga`;
            
            // Zobrazení detailu hráče
            let html = `
                <h2>${data.name}</h2>
                <div class="player-details">
            `;
            
            if (data.email) {
                html += `<p><strong>Email:</strong> ${data.email}</p>`;
            }
            
            if (data.phone) {
                html += `<p><strong>Telefon:</strong> ${data.phone}</p>`;
            }
            
            html += `
                    <p><strong>Registrován od:</strong> ${new Date(data.created_at).toLocaleDateString('cs-CZ')}</p>
                </div>
            `;
            
            playerInfoContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba při načítání detailu hráče:', error);
            playerInfoContainer.innerHTML = `<p>Nepodařilo se načíst detail hráče: ${error.message}</p>`;
        });
}

/**
 * Načte statistiky hráče z API
 * 
 * @param {number} playerId ID hráče
 */
function fetchPlayerStats(playerId) {
    const statsContainer = document.getElementById('stats-content');
    
    fetch(`php/get-player-details.php?id=${playerId}&stats=1`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se načíst statistiky hráče');
            }
            return response.json();
        })
        .then(data => {
            if (!data || !data.stats) {
                statsContainer.innerHTML = '<p>Žádné statistiky k dispozici</p>';
                return;
            }

            const stats = data.stats;
            const winRate = stats.matches > 0 
                ? Math.round((stats.wins / stats.matches) * 100) 
                : 0;
            
            // Zobrazení statistik hráče
            let html = `
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${stats.tournaments || 0}</div>
                        <div class="stat-label">Odehraných turnajů</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${stats.matches || 0}</div>
                        <div class="stat-label">Odehraných zápasů</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${stats.wins || 0}</div>
                        <div class="stat-label">Výher</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${stats.losses || 0}</div>
                        <div class="stat-label">Proher</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${winRate}%</div>
                        <div class="stat-label">Úspěšnost</div>
                    </div>
                </div>
            `;
            
            statsContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba při načítání statistik hráče:', error);
            statsContainer.innerHTML = `<p>Nepodařilo se načíst statistiky hráče: ${error.message}</p>`;
        });
}

/**
 * Načte turnaje hráče z API
 * 
 * @param {number} playerId ID hráče
 */
function fetchPlayerTournaments(playerId) {
    const tournamentsContainer = document.getElementById('tournaments-content');
    
    fetch(`php/get-tournament-players.php?player_id=${playerId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se načíst turnaje hráče');
            }
            return response.json();
        })
        .then(data => {
            if (!data || data.length === 0) {
                tournamentsContainer.innerHTML = '<p>Žádné odehrané turnaje</p>';
                return;
            }

            // Zobrazení turnajů hráče
            let html = `
                <table>
                    <thead>
                        <tr>
                            <th>Název turnaje</th>
                            <th>Datum</th>
                            <th>Umístění</th>
                            <th>Akce</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            data.forEach(tournament => {
                const startDate = tournament.start_date 
                    ? new Date(tournament.start_date).toLocaleDateString('cs-CZ') 
                    : 'Neurčeno';
                
                html += `
                    <tr>
                        <td>${tournament.name}</td>
                        <td>${startDate}</td>
                        <td>${tournament.final_rank || 'Neurčeno'}</td>
                        <td><a href="turnaj.html?id=${tournament.tournament_id}" class="btn">Detail</a></td>
                    </tr>
                `;
            });
            
            html += `
                    </tbody>
                </table>
            `;
            
            tournamentsContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba při načítání turnajů hráče:', error);
            tournamentsContainer.innerHTML = `<p>Nepodařilo se načíst turnaje hráče: ${error.message}</p>`;
        });
}

/**
 * Načte zápasy hráče z API
 * 
 * @param {number} playerId ID hráče
 */
function fetchPlayerMatches(playerId) {
    const matchesContainer = document.getElementById('matches-content');
    
    fetch(`php/get-player-details.php?id=${playerId}&matches=1`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se načíst zápasy hráče');
            }
            return response.json();
        })
        .then(data => {
            if (!data || !data.matches || data.matches.length === 0) {
                matchesContainer.innerHTML = '<p>Žádné odehrané zápasy</p>';
                return;
            }

            // Zobrazení zápasů hráče
            let html = `
                <table>
                    <thead>
                        <tr>
                            <th>Turnaj</th>
                            <th>Kolo</th>
                            <th>Soupeř</th>
                            <th>Výsledek</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            // Omezení na posledních 10 zápasů
            const recentMatches = data.matches.slice(0, 10);
            
            recentMatches.forEach(match => {
                const isPlayer1 = match.player1_id == playerId;
                const opponentId = isPlayer1 ? match.player2_id : match.player1_id;
                const opponentName = isPlayer1 ? match.player2_name : match.player1_name;
                
                let result = 'Neurčeno';
                if (match.winner_id) {
                    if (match.winner_id == playerId) {
                        result = `<span class="win">Výhra ${match.score_player1}:${match.score_player2}</span>`;
                    } else {
                        result = `<span class="loss">Prohra ${match.score_player1}:${match.score_player2}</span>`;
                    }
                }
                
                html += `
                    <tr>
                        <td><a href="turnaj.html?id=${match.tournament_id}">${match.tournament_name}</a></td>
                        <td>${match.round}</td>
                        <td><a href="hrac.html?id=${opponentId}">${opponentName || 'Neznámý soupeř'}</a></td>
                        <td>${result}</td>
                    </tr>
                `;
            });
            
            html += `
                    </tbody>
                </table>
            `;
            
            matchesContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba při načítání zápasů hráče:', error);
            matchesContainer.innerHTML = `<p>Nepodařilo se načíst zápasy hráče: ${error.message}</p>`;
        });
}
