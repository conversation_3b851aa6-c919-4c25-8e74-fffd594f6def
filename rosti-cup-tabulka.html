<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Roští Cup - Tabulka | Bruntálská šipková liga</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .standings-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .standings-table th,
        .standings-table td {
            padding: 12px 8px;
            text-align: center;
            border-bottom: 1px solid #ddd;
        }
        
        .standings-table th {
            background: #dc143c;
            color: white;
            font-weight: bold;
            position: sticky;
            top: 0;
        }
        
        .standings-table tr:hover {
            background: #f5f5f5;
        }
        
        .standings-table .position {
            font-weight: bold;
            color: #dc143c;
        }
        
        .standings-table .name {
            text-align: left;
            font-weight: bold;
        }
        
        .standings-table .points {
            font-weight: bold;
            color: #007bff;
        }
        
        .medal {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 5px;
        }
        
        .gold { background: #ffd700; }
        .silver { background: #c0c0c0; }
        .bronze { background: #cd7f32; }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #dc143c;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .tournament-history {
            display: none;
            background: #f8f9fa;
            padding: 10px;
            margin-top: 5px;
            border-radius: 5px;
        }
        
        .show-history {
            cursor: pointer;
            color: #007bff;
            text-decoration: underline;
        }
        
        .filter-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .filter-section select,
        .filter-section input {
            padding: 8px;
            margin: 0 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <h1>Bruntálská šipková liga</h1>
                <nav>
                    <ul>
                        <li><a href="index.html">Domů</a></li>
                        <li><a href="hraci.html">Hráči</a></li>
                        <li><a href="liga.html">Liga</a></li>
                        <li class="dropdown">
                            <a href="#" class="dropbtn">Turnaje</a>
                            <div class="dropdown-content">
                                <a href="blue-oyster-cup.html">Blue Oyster Cup</a>
                                <a href="sob-cup.html">Sob Cup</a></li>
                                <a href="rosti-cup.html">Roští Cup</a>
                            </div>
                        </li>
                        <li><a href="pravidla.html">Pravidla</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <h1>Roští Cup - Tabulka</h1>
            
            <div class="filter-section">
                <label>Zobrazit:</label>
                <select id="limit-select">
                    <option value="20">Top 20</option>
                    <option value="50" selected>Top 50</option>
                    <option value="100">Top 100</option>
                    <option value="0">Všechny</option>
                </select>
                
                <label>Hledat hráče:</label>
                <input type="text" id="player-search" placeholder="Zadejte jméno hráče...">
                
                <button onclick="loadStandings()" class="btn">Aktualizovat</button>
            </div>
            
            <div id="loading" style="text-align: center; padding: 20px;">
                <p>Načítání tabulky...</p>
            </div>
            
            <div id="stats-section" class="stats-grid" style="display: none;"></div>
            
            <div id="standings-section" style="display: none;">
                <h2>Tabulka hráčů</h2>
                <div class="table-container">
                    <table id="standings-table" class="standings-table">
                        <thead>
                            <tr>
                                <th>Pořadí</th>
                                <th>Jméno</th>
                                <th>Účasti</th>
                                <th>Body</th>
                                <th>Průměr</th>
                                <th>Nejlepší</th>
                                <th>Výhry</th>
                                <th>Pódium</th>
                                <th>Top 8</th>
                                <th>% Výher</th>
                                <th>% Pódium</th>
                                <th>Historie</th>
                            </tr>
                        </thead>
                        <tbody id="standings-tbody">
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div id="recent-tournaments" style="display: none;">
                <h2>Nejnovější turnaje</h2>
                <div id="recent-tournaments-list"></div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 Bruntálská šipková liga. Všechna práva vyhrazena.</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadStandings();
        });

        function loadStandings() {
            const limit = document.getElementById('limit-select').value;
            const searchTerm = document.getElementById('player-search').value.toLowerCase();
            
            document.getElementById('loading').style.display = 'block';
            document.getElementById('stats-section').style.display = 'none';
            document.getElementById('standings-section').style.display = 'none';
            document.getElementById('recent-tournaments').style.display = 'none';
            
            const url = limit === '0' 
                ? 'php/get-category-standings.php?category=Roští Cup&limit=1000'
                : `php/get-category-standings.php?category=Roští Cup&limit=${limit}`;
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loading').style.display = 'none';
                    
                    if (data.success) {
                        displayStats(data.overall_stats);
                        displayStandings(data.standings, searchTerm);
                        displayRecentTournaments(data.recent_tournaments);
                    } else {
                        alert('Chyba při načítání dat: ' + data.message);
                    }
                })
                .catch(error => {
                    document.getElementById('loading').style.display = 'none';
                    console.error('Chyba:', error);
                    alert('Chyba při načítání dat');
                });
        }

        function displayStats(stats) {
            const statsSection = document.getElementById('stats-section');
            
            statsSection.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${stats.total_tournaments || 0}</div>
                    <div class="stat-label">Celkem turnajů</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.unique_players || 0}</div>
                    <div class="stat-label">Aktivních hráčů</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.total_points_awarded || 0}</div>
                    <div class="stat-label">Udělených bodů</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${Math.round(stats.avg_tournament_size || 0)}</div>
                    <div class="stat-label">Průměrná účast</div>
                </div>
            `;
            
            statsSection.style.display = 'grid';
        }

        function displayStandings(standings, searchTerm) {
            const tbody = document.getElementById('standings-tbody');
            tbody.innerHTML = '';
            
            // Filtrování podle vyhledávání
            const filteredStandings = searchTerm 
                ? standings.filter(player => player.name.toLowerCase().includes(searchTerm))
                : standings;
            
            filteredStandings.forEach((player, index) => {
                const row = document.createElement('tr');
                
                // Medaile pro top 3
                let medal = '';
                if (player.position === 1) medal = '<span class="medal gold"></span>';
                else if (player.position === 2) medal = '<span class="medal silver"></span>';
                else if (player.position === 3) medal = '<span class="medal bronze"></span>';
                
                row.innerHTML = `
                    <td class="position">${medal}${player.position}.</td>
                    <td class="name">${player.name}</td>
                    <td>${player.tournaments_played}</td>
                    <td class="points">${player.total_points}</td>
                    <td>${player.avg_points}</td>
                    <td>${player.best_rank}</td>
                    <td>${player.wins}</td>
                    <td>${player.podiums}</td>
                    <td>${player.top8}</td>
                    <td>${player.win_rate}%</td>
                    <td>${player.podium_rate}%</td>
                    <td>
                        <span class="show-history" onclick="toggleHistory(${index})">Zobrazit</span>
                        <div id="history-${index}" class="tournament-history">
                            ${formatTournamentHistory(player.tournament_history)}
                        </div>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
            
            document.getElementById('standings-section').style.display = 'block';
        }

        function formatTournamentHistory(history) {
            if (!history || history.length === 0) {
                return '<p>Žádná historie</p>';
            }
            
            let html = '<table style="width: 100%; font-size: 0.9em;"><tr><th>Turnaj</th><th>Umístění</th><th>Body</th><th>Datum</th></tr>';
            
            history.slice(0, 10).forEach(tournament => {
                html += `
                    <tr>
                        <td>${tournament.tournament_name}</td>
                        <td>${tournament.final_rank}</td>
                        <td>${tournament.points}</td>
                        <td>${tournament.date || 'N/A'}</td>
                    </tr>
                `;
            });
            
            html += '</table>';
            return html;
        }

        function toggleHistory(index) {
            const historyDiv = document.getElementById(`history-${index}`);
            const isVisible = historyDiv.style.display === 'block';
            
            // Skrýt všechny historie
            document.querySelectorAll('.tournament-history').forEach(div => {
                div.style.display = 'none';
            });
            
            // Zobrazit/skrýt vybranou historii
            if (!isVisible) {
                historyDiv.style.display = 'block';
            }
        }

        function displayRecentTournaments(tournaments) {
            const section = document.getElementById('recent-tournaments-list');
            
            if (!tournaments || tournaments.length === 0) {
                section.innerHTML = '<p>Žádné turnaje k zobrazení</p>';
            } else {
                let html = '<table class="standings-table"><tr><th>Název</th><th>Datum</th><th>Účastníci</th><th>Vítěz</th></tr>';
                
                tournaments.forEach(tournament => {
                    const date = tournament.start_date ? new Date(tournament.start_date).toLocaleDateString('cs-CZ') : 'N/A';
                    html += `
                        <tr>
                            <td>${tournament.name}</td>
                            <td>${date}</td>
                            <td>${tournament.participants_count || 0}</td>
                            <td>${tournament.winner || 'N/A'}</td>
                        </tr>
                    `;
                });
                
                html += '</table>';
                section.innerHTML = html;
            }
            
            document.getElementById('recent-tournaments').style.display = 'block';
        }

        // Vyhledávání v reálném čase
        document.getElementById('player-search').addEventListener('input', function() {
            loadStandings();
        });
    </script>
</body>
</html>
