<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administrace - Bruntálská šipková liga</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body class="admin-page">
    <header class="admin-header">
        <div class="container">
            <div class="admin-nav">
                <h1>Administrace Bruntálské šipkové ligy</h1>
                <ul>
                    <li><a href="index.html" class="active">Dashboard</a></li>
                    <li><a href="challonge.html">Challonge</a></li>
                    <li><a href="pravidla.html">Pravidla</a></li>
                </ul>
                <div class="admin-user-info">
                    <span id="user-name">Uživatel</span>
                    <a href="#" id="logout-btn" class="btn btn-sm btn-secondary">Odhlásit se</a>
                </div>
            </div>
        </div>
    </header>

    <main class="admin-container">
        <div class="container">
            <div id="admin-message"></div>
            
            <div class="dashboard-cards">
                <div class="dashboard-card">
                    <h3>Hráči</h3>
                    <div class="card-value" id="players-count">0</div>
                    <div class="card-label">Celkový počet hráčů</div>
                </div>
                <div class="dashboard-card">
                    <h3>Turnaje</h3>
                    <div class="card-value" id="tournaments-count">0</div>
                    <div class="card-label">Celkový počet turnajů</div>
                </div>
                <div class="dashboard-card">
                    <h3>Zápasy</h3>
                    <div class="card-value" id="matches-count">0</div>
                    <div class="card-label">Celkový počet zápasů</div>
                </div>
                <div class="dashboard-card">
                    <h3>Poslední synchronizace</h3>
                    <div class="card-value" id="last-sync">-</div>
                    <div class="card-label">Datum a čas poslední synchronizace</div>
                </div>
            </div>
            
            <div class="admin-section">
                <h2>Poslední turnaje</h2>
                <div id="recent-tournaments">
                    <p>Načítání turnajů...</p>
                </div>
            </div>
            
            <div class="admin-section">
                <h2>Nejlepší hráči</h2>
                <div id="top-players">
                    <p>Načítání hráčů...</p>
                </div>
            </div>
        </div>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Kontrola, zda je uživatel přihlášen
            checkLogin();
            
            // Načtení dat pro dashboard
            loadDashboardData();
            
            // Obsluha odhlášení
            document.getElementById('logout-btn').addEventListener('click', function(e) {
                e.preventDefault();
                logout();
            });
        });
        
        /**
         * Kontrola, zda je uživatel přihlášen
         */
        function checkLogin() {
            fetch('../php/check-login.php')
                .then(response => response.json())
                .then(data => {
                    if (!data.logged_in) {
                        // Přesměrování na přihlašovací stránku
                        window.location.href = 'login.html';
                    } else {
                        // Zobrazení jména přihlášeného uživatele
                        document.getElementById('user-name').textContent = data.username;
                    }
                })
                .catch(error => {
                    console.error('Chyba při kontrole přihlášení:', error);
                    window.location.href = 'login.html';
                });
        }
        
        /**
         * Odhlášení uživatele
         */
        function logout() {
            fetch('../php/logout.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = 'login.html';
                    } else {
                        document.getElementById('admin-message').innerHTML = `<div class="alert alert-error">${data.message}</div>`;
                    }
                })
                .catch(error => {
                    console.error('Chyba při odhlašování:', error);
                    document.getElementById('admin-message').innerHTML = '<div class="alert alert-error">Chyba při odhlašování. Zkuste to prosím znovu.</div>';
                });
        }
        
        /**
         * Načtení dat pro dashboard
         */
        function loadDashboardData() {
            // Načtení statistik
            fetch('../php/get-admin-stats.php')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        document.getElementById('admin-message').innerHTML = `<div class="alert alert-error">${data.error}</div>`;
                        return;
                    }
                    
                    // Zobrazení statistik
                    document.getElementById('players-count').textContent = data.players_count || 0;
                    document.getElementById('tournaments-count').textContent = data.tournaments_count || 0;
                    document.getElementById('matches-count').textContent = data.matches_count || 0;
                    
                    // Formátování času poslední synchronizace
                    if (data.last_sync_time) {
                        const syncDate = new Date(data.last_sync_time);
                        document.getElementById('last-sync').textContent = syncDate.toLocaleString('cs-CZ');
                    } else {
                        document.getElementById('last-sync').textContent = 'Nikdy';
                    }
                })
                .catch(error => {
                    console.error('Chyba při načítání statistik:', error);
                    document.getElementById('admin-message').innerHTML = '<div class="alert alert-error">Chyba při načítání statistik. Zkuste to prosím znovu.</div>';
                });
            
            // Načtení posledních turnajů
            fetch('../php/get-tournaments.php?limit=5')
                .then(response => response.json())
                .then(data => {
                    const tournamentsContainer = document.getElementById('recent-tournaments');
                    
                    if (!data || data.length === 0) {
                        tournamentsContainer.innerHTML = '<p>Žádné turnaje k zobrazení</p>';
                        return;
                    }
                    
                    // Zobrazení posledních turnajů
                    let html = `
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>Název</th>
                                    <th>Kategorie</th>
                                    <th>Datum</th>
                                    <th>Status</th>
                                    <th>Akce</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;
                    
                    data.forEach(tournament => {
                        const startDate = tournament.start_date 
                            ? new Date(tournament.start_date).toLocaleDateString('cs-CZ') 
                            : 'Neurčeno';
                        const status = getStatusText(tournament.status);
                        
                        html += `
                            <tr>
                                <td>${tournament.name}</td>
                                <td>${tournament.category_name || 'Nezařazeno'}</td>
                                <td>${startDate}</td>
                                <td>${status}</td>
                                <td>
                                    <div class="actions">
                                        <a href="../turnaj.html?id=${tournament.id}" class="btn btn-sm btn-primary" target="_blank">Zobrazit</a>
                                    </div>
                                </td>
                            </tr>
                        `;
                    });
                    
                    html += `
                            </tbody>
                        </table>
                    `;
                    
                    tournamentsContainer.innerHTML = html;
                })
                .catch(error => {
                    console.error('Chyba při načítání turnajů:', error);
                    document.getElementById('recent-tournaments').innerHTML = '<p>Chyba při načítání turnajů</p>';
                });
            
            // Načtení nejlepších hráčů
            fetch('../php/get-players-stats.php?limit=5')
                .then(response => response.json())
                .then(data => {
                    const playersContainer = document.getElementById('top-players');
                    
                    if (!data || data.length === 0) {
                        playersContainer.innerHTML = '<p>Žádní hráči k zobrazení</p>';
                        return;
                    }
                    
                    // Zobrazení nejlepších hráčů
                    let html = `
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>Jméno</th>
                                    <th>Odehrané turnaje</th>
                                    <th>Výhry</th>
                                    <th>Prohry</th>
                                    <th>Úspěšnost</th>
                                    <th>Akce</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;
                    
                    data.forEach(player => {
                        const winRate = player.matches > 0 
                            ? Math.round((player.wins / player.matches) * 100) 
                            : 0;
                        
                        html += `
                            <tr>
                                <td>${player.name}</td>
                                <td>${player.tournaments}</td>
                                <td>${player.wins}</td>
                                <td>${player.losses}</td>
                                <td>${winRate}%</td>
                                <td>
                                    <div class="actions">
                                        <a href="../hrac.html?id=${player.id}" class="btn btn-sm btn-primary" target="_blank">Zobrazit</a>
                                    </div>
                                </td>
                            </tr>
                        `;
                    });
                    
                    html += `
                            </tbody>
                        </table>
                    `;
                    
                    playersContainer.innerHTML = html;
                })
                .catch(error => {
                    console.error('Chyba při načítání hráčů:', error);
                    document.getElementById('top-players').innerHTML = '<p>Chyba při načítání hráčů</p>';
                });
        }
        
        /**
         * Převede status turnaje na čitelný text
         * 
         * @param {string} status Status turnaje
         * @return {string} Čitelný text statusu
         */
        function getStatusText(status) {
            switch (status) {
                case 'pending':
                    return 'Čeká na zahájení';
                case 'underway':
                    return 'Probíhá';
                case 'complete':
                    return 'Dokončeno';
                default:
                    return 'Neurčeno';
            }
        }
    </script>
</body>
</html>
