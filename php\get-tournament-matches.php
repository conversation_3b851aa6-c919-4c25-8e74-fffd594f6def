<?php
/**
 * Skript pro získ<PERSON><PERSON> z<PERSON>ů turnaje
 */

// Připojení k <PERSON>bázi
require_once 'db-connection.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

// Kontrola, zda byl zadán ID turnaje
if (!isset($_GET['tournament_id']) || !is_numeric($_GET['tournament_id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Chybí ID turnaje']);
    exit;
}

$tournamentId = (int) $_GET['tournament_id'];

try {
    // Získání zápasů turnaje
    $query = "
        SELECT 
            m.*,
            p1.name AS player1_name,
            p2.name AS player2_name
        FROM 
            matches m
        LEFT JOIN 
            players p1 ON m.player1_id = p1.id
        LEFT JOIN 
            players p2 ON m.player2_id = p2.id
        WHERE 
            m.tournament_id = ?
        ORDER BY 
            m.round, m.id
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute([$tournamentId]);
    $matches = $stmt->fetchAll();
    
    // Vrácení zápasů turnaje jako JSON
    echo json_encode($matches);
} catch (PDOException $e) {
    // Vrácení chybové zprávy jako JSON
    http_response_code(500);
    echo json_encode(['error' => 'Nepodařilo se získat zápasy turnaje: ' . $e->getMessage()]);
}
