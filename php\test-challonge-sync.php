<?php
/**
 * Testovací skript pro ověřen<PERSON> funkčnosti Challonge API a synchronizace
 */

// Připojení k databázi a Challonge API
require_once 'db-connection.php';
require_once 'challonge-api.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

// Spuštění session
session_start();

// Kontrola, zda je uživatel přihlášen
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('HTTP/1.1 401 Unauthorized');
    echo json_encode(['success' => false, 'message' => 'Nejste přihlášen']);
    exit;
}

try {
    $testResults = [];
    
    // Test 1: Připojení k Challonge API
    $testResults['api_connection'] = [
        'name' => 'Připojení k Challonge API',
        'status' => 'testing'
    ];
    
    try {
        $tournaments = $challongeApi->getTournaments();
        $testResults['api_connection']['status'] = 'success';
        $testResults['api_connection']['message'] = 'Připojení úspěšné';
        $testResults['api_connection']['tournaments_count'] = count($tournaments);
    } catch (Exception $e) {
        $testResults['api_connection']['status'] = 'error';
        $testResults['api_connection']['message'] = 'Chyba připojení: ' . $e->getMessage();
        echo json_encode(['success' => false, 'tests' => $testResults]);
        exit;
    }
    
    // Test 2: Analýza prvních 10 turnajů pro kategorizaci
    $testResults['categorization'] = [
        'name' => 'Analýza kategorizace turnajů',
        'status' => 'testing'
    ];
    
    $categoryAnalysis = [];
    $sampleTournaments = array_slice($tournaments, 0, 10);
    
    foreach ($sampleTournaments as $tournamentData) {
        $tournament = $tournamentData['tournament'];
        $category = $challongeApi->categorizeTournament($tournament['name']);
        
        $categoryAnalysis[] = [
            'name' => $tournament['name'],
            'category' => $category,
            'state' => $tournament['state'],
            'participants' => $tournament['participants_count'],
            'created' => $tournament['created_at']
        ];
    }
    
    $testResults['categorization']['status'] = 'success';
    $testResults['categorization']['sample_tournaments'] = $categoryAnalysis;
    
    // Test 3: Kontrola databázových tabulek
    $testResults['database'] = [
        'name' => 'Kontrola databázových tabulek',
        'status' => 'testing'
    ];
    
    $requiredTables = [
        'tournaments',
        'tournament_participants', 
        'tournament_categories',
        'settings'
    ];
    
    $missingTables = [];
    foreach ($requiredTables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
        if (!$stmt->fetch()) {
            $missingTables[] = $table;
        }
    }
    
    if (empty($missingTables)) {
        $testResults['database']['status'] = 'success';
        $testResults['database']['message'] = 'Všechny potřebné tabulky existují';
    } else {
        $testResults['database']['status'] = 'error';
        $testResults['database']['message'] = 'Chybí tabulky: ' . implode(', ', $missingTables);
    }
    
    // Test 4: Kontrola kategorií turnajů
    $testResults['categories'] = [
        'name' => 'Kontrola kategorií turnajů',
        'status' => 'testing'
    ];
    
    $stmt = $pdo->query("SELECT id, name FROM tournament_categories");
    $categories = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    
    $requiredCategories = ['Blue Oyster Cup', 'Sob Cup', 'Roští Cup', 'Speciální turnaje'];
    $missingCategories = [];
    
    foreach ($requiredCategories as $category) {
        if (!in_array($category, $categories)) {
            $missingCategories[] = $category;
        }
    }
    
    if (empty($missingCategories)) {
        $testResults['categories']['status'] = 'success';
        $testResults['categories']['message'] = 'Všechny kategorie existují';
        $testResults['categories']['categories'] = $categories;
    } else {
        $testResults['categories']['status'] = 'warning';
        $testResults['categories']['message'] = 'Chybí kategorie: ' . implode(', ', $missingCategories);
        $testResults['categories']['existing'] = $categories;
    }
    
    // Test 5: Test bodovacího systému
    $testResults['scoring'] = [
        'name' => 'Test bodovacího systému',
        'status' => 'testing'
    ];
    
    $scoringTests = [
        1 => 100, 2 => 90, 3 => 80, 4 => 70,
        5 => 60, 6 => 60, 7 => 50, 8 => 50,
        9 => 40, 12 => 40, 13 => 30, 17 => 30,
        18 => 20, 25 => 20, 26 => 10, 32 => 10,
        33 => 0, 50 => 0
    ];
    
    $scoringResults = [];
    foreach ($scoringTests as $position => $expectedPoints) {
        $actualPoints = $challongeApi->calculatePoints($position);
        $scoringResults[] = [
            'position' => $position,
            'expected' => $expectedPoints,
            'actual' => $actualPoints,
            'correct' => $actualPoints === $expectedPoints
        ];
    }
    
    $allCorrect = array_reduce($scoringResults, function($carry, $item) {
        return $carry && $item['correct'];
    }, true);
    
    $testResults['scoring']['status'] = $allCorrect ? 'success' : 'error';
    $testResults['scoring']['message'] = $allCorrect ? 'Bodovací systém funguje správně' : 'Chyba v bodovacím systému';
    $testResults['scoring']['tests'] = $scoringResults;
    
    // Celkové vyhodnocení
    $allTestsPassed = true;
    foreach ($testResults as $test) {
        if ($test['status'] === 'error') {
            $allTestsPassed = false;
            break;
        }
    }
    
    echo json_encode([
        'success' => $allTestsPassed,
        'message' => $allTestsPassed ? 'Všechny testy prošly úspěšně' : 'Některé testy selhaly',
        'tests' => $testResults,
        'ready_for_sync' => $allTestsPassed
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Chyba při testování: ' . $e->getMessage()
    ]);
}
?>
