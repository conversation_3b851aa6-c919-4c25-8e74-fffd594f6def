/* Administrač<PERSON><PERSON> r<PERSON>í - styly */

/* Základní styly pro administraci */
.admin-page {
    background-color: #f8f9fa;
}

.admin-container {
    padding: 2rem 0;
}

.admin-header {
    background-color: #343a40;
    color: #fff;
    padding: 1rem 0;
    margin-bottom: 2rem;
}

.admin-header h1 {
    margin: 0;
    font-size: 1.8rem;
}

.admin-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-nav ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.admin-nav ul li {
    margin-right: 1.5rem;
}

.admin-nav ul li a {
    color: #fff;
    text-decoration: none;
    font-weight: bold;
    transition: color 0.3s;
}

.admin-nav ul li a:hover {
    color: #17a2b8;
}

.admin-user-info {
    display: flex;
    align-items: center;
}

.admin-user-info span {
    margin-right: 1rem;
}

/* <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>í stránka */
.login-page {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f8f9fa;
}

.login-container {
    width: 100%;
    max-width: 400px;
    padding: 0 15px;
}

.login-box {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 2rem;
}

.login-box h1 {
    margin-top: 0;
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    text-align: center;
}

.login-box .form-group {
    margin-bottom: 1.5rem;
}

.login-box label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.login-box input[type="text"],
.login-box input[type="password"] {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.login-box button {
    width: 100%;
    padding: 0.75rem;
    background-color: #007bff;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.login-box button:hover {
    background-color: #0069d9;
}

.login-box .back-link {
    text-align: center;
    margin-top: 1.5rem;
    margin-bottom: 0;
}

/* Alerty */
.alert {
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.25rem;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-error {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* Karty na dashboardu */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
}

.dashboard-card h3 {
    margin-top: 0;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.dashboard-card .card-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.dashboard-card .card-label {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Tabulky */
.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 2rem;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.admin-table th,
.admin-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.admin-table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.admin-table tr:hover {
    background-color: #f8f9fa;
}

.admin-table .actions {
    display: flex;
    gap: 0.5rem;
}

/* Formuláře */
.admin-form {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.admin-form .form-group {
    margin-bottom: 1.5rem;
}

.admin-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.admin-form input[type="text"],
.admin-form input[type="email"],
.admin-form input[type="password"],
.admin-form select,
.admin-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.admin-form textarea {
    min-height: 150px;
}

.admin-form .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1.5rem;
}

/* Tlačítka */
.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0.25rem;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    cursor: pointer;
}

.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    color: #fff;
    background-color: #0069d9;
    border-color: #0062cc;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62;
}

.btn-success {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;
}

.btn-success:hover {
    color: #fff;
    background-color: #218838;
    border-color: #1e7e34;
}

.btn-danger {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-danger:hover {
    color: #fff;
    background-color: #c82333;
    border-color: #bd2130;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

.btn-info {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8;
}

.btn-info:hover {
    color: #fff;
    background-color: #138496;
    border-color: #117a8b;
}

/* Styly pro testování a statistiky */
.test-result {
    padding: 15px;
    margin: 10px 0;
    border-radius: 5px;
    border-left: 4px solid;
}

.test-result.success {
    background-color: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.test-result.warning {
    background-color: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.test-result.error {
    background-color: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.stats-container {
    margin-top: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.stat-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #dee2e6;
}

.card-value {
    font-size: 2rem;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 5px;
}

.card-label {
    font-size: 0.9rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Responzivní design */
@media (max-width: 768px) {
    .admin-container {
        margin: 0;
        padding: 10px;
    }

    .admin-header h1 {
        font-size: 1.5rem;
    }

    .admin-nav ul {
        flex-direction: column;
    }

    .admin-nav li {
        margin: 5px 0;
    }

    .form-actions {
        flex-direction: column;
    }

    .btn {
        margin: 5px 0;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}
