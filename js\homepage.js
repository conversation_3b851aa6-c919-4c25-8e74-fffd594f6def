document.addEventListener('DOMContentLoaded', function() {
    // Nacteni dat pro hlavni stranku
    loadRoundSchedule();
    loadUpcomingEvents();
    loadSpecialTournaments();
    loadWorldNews();
    loadCzechNews();
    loadTournamentCountdowns();

    // Pridani event listeneru pro kliknuti na odpocty
    setupCountdownClickHandlers();
});



/**
 * Načte nadcházející a<PERSON>ce
 */
function loadUpcomingEvents() {
    fetch('php/get-tournaments.php')
        .then(response => response.json())
        .then(data => {
            const eventsContainer = document.getElementById('upcoming-events');

            if (!data || data.length === 0) {
                eventsContainer.innerHTML = '<p>Zadne nadchazejici akce</p>';
                return;
            }

            const today = new Date();
            const nextWeek = new Date();
            nextWeek.setDate(today.getDate() + 7);

            // Filtrování turnajů na příští týden
            const upcomingEvents = data.filter(tournament => {
                if (!tournament.start_date) return false;
                const tournamentDate = new Date(tournament.start_date);
                return tournamentDate >= today && tournamentDate <= nextWeek;
            }).sort((a, b) => new Date(a.start_date) - new Date(b.start_date));

            if (upcomingEvents.length === 0) {
                eventsContainer.innerHTML = '<p>Zadne akce v pristim tydnu</p>';
                return;
            }

            let html = '';
            upcomingEvents.forEach(event => {
                const eventDate = new Date(event.start_date);
                const day = eventDate.getDate();
                const month = eventDate.toLocaleDateString('cs-CZ', { month: 'short' });

                html += `
                    <div class="event-item">
                        <div class="event-date">
                            <span class="day">${day}</span>
                            <span class="month">${month}</span>
                        </div>
                        <div class="event-info">
                            <h5>${event.name}</h5>
                            <p>${event.category_name || 'Turnaj'}</p>
                        </div>
                    </div>
                `;
            });

            eventsContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba pri nacitani nadchazejicich akci:', error);
            document.getElementById('upcoming-events').innerHTML = '<p>Chyba pri nacitani akci</p>';
        });
}

/**
 * Nacte novinky ze sveta sipek
 */
function loadWorldNews() {
    const worldNewsContainer = document.getElementById('world-news');

    // Simulace novinek ze sveta (v realne aplikaci by se nacitaly z RSS nebo API)
    const worldNews = [
        {
            title: "PDC World Championship 2025 - Finale se blizi",
            content: "Svetove mistrovstvi v sipkach PDC pokracuje vzrusujicimi zapasy. Do finale postupuji nejlepsi hraci sveta.",
            date: "2025-01-15"
        },
        {
            title: "Michael van Gerwen obhajuje titul",
            content: "Holandsky sipkar Michael van Gerwen pokracuje ve sve dominanci na svetove scene.",
            date: "2025-01-12"
        },
        {
            title: "Nova pravidla pro profesionalni turnaje",
            content: "PDC oznamila zmeny v pravidlech pro nadchazejici sezonu 2025.",
            date: "2025-01-10"
        },
        {
            title: "Rekordni navstevnost na Premier League",
            content: "Premier League Darts zaznamenala rekordni navstevnost v letosni sezone.",
            date: "2025-01-08"
        }
    ];

    let html = '';
    worldNews.forEach(news => {
        const newsDate = new Date(news.date);
        const formattedDate = newsDate.toLocaleDateString('cs-CZ');

        html += `
            <div class="news-item">
                <h4>${news.title}</h4>
                <p>${news.content}</p>
                <div class="news-date">${formattedDate}</div>
            </div>
        `;
    });

    worldNewsContainer.innerHTML = html;
}

/**
 * Nacte novinky z Ceske republiky
 */
function loadCzechNews() {
    const czechNewsContainer = document.getElementById('czech-news-content');

    // Simulace ceskych novinek (v realne aplikaci by se nacitaly z databaze nebo API)
    const czechNews = [
        {
            title: "Bruntalska liga pokracuje",
            content: "Nove kolo Bruntalske sipkove ligy prinasi napinave souboje.",
            date: "2025-01-20"
        },
        {
            title: "Cesky sampionat 2025",
            content: "Prihlasky na cesky sampionat v sipkach jsou otevrene.",
            date: "2025-01-18"
        },
        {
            title: "Novy rekord v Praze",
            content: "Prazsky turnaj zaznamenal rekordni ucast 180 hracu.",
            date: "2025-01-15"
        }
    ];

    let html = '';
    czechNews.forEach(news => {
        const newsDate = new Date(news.date);
        const formattedDate = newsDate.toLocaleDateString('cs-CZ');

        html += `
            <div class="news-item">
                <h4>${news.title}</h4>
                <p>${news.content}</p>
                <div class="news-date">${formattedDate}</div>
            </div>
        `;
    });

    czechNewsContainer.innerHTML = html;
}

/**
 * Nacte odpocty do turnaju
 */
function loadTournamentCountdowns() {
    // Blue Oyster Cup - kazde utery
    const blueOysterDays = calculateDaysToNextWeekday(2); // utery = 2
    document.getElementById('blue-oyster-days').textContent = blueOysterDays;

    // Rosti Cup - kazdou stredu od zari do kvetna (30 tydnu)
    const rostiDays = calculateDaysToRostiCup();
    document.getElementById('rosti-days').textContent = rostiDays;

    // Sob Cup - kazdy ctvrtek od rijna do kvetna (30 tydnu)
    const sobDays = calculateDaysToSobCup();
    document.getElementById('sob-days').textContent = sobDays;
}

/**
 * Vypočítá dny do dalšího dne v týdnu
 * @param {number} targetDay Den v týdnu (0=neděle, 1=pondělí, ..., 6=sobota)
 * @returns {number} Počet dní
 */
function calculateDaysToNextWeekday(targetDay) {
    const today = new Date();
    const currentDay = today.getDay();

    let daysUntil = targetDay - currentDay;
    if (daysUntil <= 0) {
        daysUntil += 7; // Pokud je den už prošlý tento týden, počítáme příští týden
    }

    return daysUntil;
}

/**
 * Vypočítá dny do dalšího Roští Cupu (středa, září-květen)
 * @returns {number} Počet dní
 */
function calculateDaysToRostiCup() {
    const today = new Date();
    const currentMonth = today.getMonth(); // 0=leden, 8=září, 4=květen

    // Roští Cup se hraje od září (8) do května (4) následujícího roku
    // Pauza je v červnu, červenci a srpnu

    // Pokud jsme v období pauzy (červen-srpen)
    if (currentMonth >= 5 && currentMonth <= 7) {
        // Počítáme do prvního září
        const nextSeptember = new Date(today.getFullYear(), 8, 1); // 1. září
        const firstWednesday = getFirstWeekdayOfMonth(nextSeptember, 3); // první středa v září
        return Math.ceil((firstWednesday - today) / (1000 * 60 * 60 * 24));
    }

    // Jinak počítáme do nejbližší středy
    return calculateDaysToNextWeekday(3); // středa = 3
}

/**
 * Vypočítá dny do dalšího Sob Cupu (čtvrtek, říjen-květen)
 * @returns {number} Počet dní
 */
function calculateDaysToSobCup() {
    const today = new Date();
    const currentMonth = today.getMonth(); // 0=leden, 9=říjen, 4=květen

    // Sob Cup se hraje od října (9) do května (4) následujícího roku
    // Pauza je v červnu, červenci, srpnu a září

    // Pokud jsme v období pauzy (červen-září)
    if (currentMonth >= 5 && currentMonth <= 8) {
        // Počítáme do prvního října
        const nextOctober = new Date(today.getFullYear(), 9, 1); // 1. říjen
        const firstThursday = getFirstWeekdayOfMonth(nextOctober, 4); // první čtvrtek v říjnu
        return Math.ceil((firstThursday - today) / (1000 * 60 * 60 * 24));
    }

    // Jinak počítáme do nejbližšího čtvrtka
    return calculateDaysToNextWeekday(4); // čtvrtek = 4
}

/**
 * Najde první výskyt daného dne v týdnu v měsíci
 * @param {Date} date Datum v měsíci
 * @param {number} weekday Den v týdnu (0=neděle, 1=pondělí, ..., 6=sobota)
 * @returns {Date} Datum prvního výskytu
 */
function getFirstWeekdayOfMonth(date, weekday) {
    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
    const firstWeekday = firstDay.getDay();

    let daysToAdd = weekday - firstWeekday;
    if (daysToAdd < 0) {
        daysToAdd += 7;
    }

    return new Date(date.getFullYear(), date.getMonth(), 1 + daysToAdd);
}

/**
 * Nastaví event handlery pro kliknutí na odpočty turnajů
 */
function setupCountdownClickHandlers() {
    // Blue Oyster Cup
    document.getElementById('blue-oyster-countdown').addEventListener('click', function() {
        // TODO: Přesměrování na stránku Blue Oyster Cup turnaje
        console.log('Klik na Blue Oyster Cup - přesměrování na turnaj');
        // window.location.href = 'turnaje/blue-oyster-cup.html';
    });

    // Roští Cup
    document.getElementById('rosti-countdown').addEventListener('click', function() {
        // TODO: Přesměrování na stránku Roští Cup turnaje
        console.log('Klik na Roští Cup - přesměrování na turnaj');
        // window.location.href = 'turnaje/rosti-cup.html';
    });

    // Sob Cup
    document.getElementById('sob-countdown').addEventListener('click', function() {
        // TODO: Přesměrování na stránku Sob Cup turnaje
        console.log('Klik na Sob Cup - přesměrování na turnaj');
        // window.location.href = 'turnaje/sob-cup.html';
    });
}

/**
 * Načte program nadcházejícího kola
 */
function loadRoundSchedule() {
    const scheduleContainer = document.getElementById('round-schedule-content');

    // Simulace programu kola pro 16 týmů (8 zápasů)
    const matches = [
        {
            round: "1. kolo",
            time: "19:00",
            team1: { name: "Sipkari Bruntal", players: "Novak, Svoboda" },
            team2: { name: "Darts Masters", players: "Prochazka, Dvorak" }
        },
        {
            round: "1. kolo",
            time: "19:30",
            team1: { name: "Bulls Eye Club", players: "Cerny, Bily" },
            team2: { name: "Triple Twenty", players: "Vesely, Smutny" }
        },
        {
            round: "1. kolo",
            time: "20:00",
            team1: { name: "Finish Line", players: "Rychly, Pomaly" },
            team2: { name: "180 Warriors", players: "Silny, Slaby" }
        },
        {
            round: "1. kolo",
            time: "20:30",
            team1: { name: "Dart Devils", players: "Horky, Studeny" },
            team2: { name: "Precision Crew", players: "Presny, Nepresny" }
        },
        {
            round: "1. kolo",
            time: "21:00",
            team1: { name: "Checkout Kings", players: "Kral, Princ" },
            team2: { name: "Double Trouble", players: "Dvojka, Trojka" }
        },
        {
            round: "1. kolo",
            time: "21:30",
            team1: { name: "Oche Legends", players: "Legenda, Mytus" },
            team2: { name: "Tungsten Titans", players: "Titan, Gigant" }
        },
        {
            round: "1. kolo",
            time: "22:00",
            team1: { name: "Flight Path", players: "Letec, Pilot" },
            team2: { name: "Board Breakers", players: "Kladivo, Bourak" }
        },
        {
            round: "1. kolo",
            time: "22:30",
            team1: { name: "Dart Storm", players: "Boure, Vichr" },
            team2: { name: "Perfect Game", players: "Dokonaly, Perfektni" }
        }
    ];

    let html = '<div class="matches-grid">';

    matches.forEach(match => {
        html += `
            <div class="match-card">
                <div class="match-header">
                    <span class="match-round">${match.round}</span>
                    <span class="match-time">${match.time}</span>
                </div>
                <div class="match-teams">
                    <div class="team">
                        <div class="team-name">${match.team1.name}</div>
                        <div class="team-players">${match.team1.players}</div>
                    </div>
                    <div class="vs-separator">VS</div>
                    <div class="team">
                        <div class="team-name">${match.team2.name}</div>
                        <div class="team-players">${match.team2.players}</div>
                    </div>
                </div>
            </div>
        `;
    });

    html += '</div>';
    scheduleContainer.innerHTML = html;
}

/**
 * Načte speciální turnaje
 */
function loadSpecialTournaments() {
    const specialContainer = document.getElementById('special-tournaments-content');

    // Simulace specialnich turnaju
    const specialTournaments = [
        {
            name: "Vanocni turnaj 2025",
            date: "2025-12-26",
            icon: "🎄",
            description: "Tradicni vanocni turnaj"
        },
        {
            name: "Novorocni cup",
            date: "2026-01-02",
            icon: "🎊",
            description: "Novorocni turnaj"
        },
        {
            name: "Velikonocni special",
            date: "2025-04-20",
            icon: "🐰",
            description: "Velikonocni turnaj"
        }
    ];

    // Filtrovani budoucich turnaju
    const today = new Date();
    const upcomingSpecial = specialTournaments.filter(tournament => {
        return new Date(tournament.date) > today;
    }).sort((a, b) => new Date(a.date) - new Date(b.date));

    if (upcomingSpecial.length === 0) {
        specialContainer.innerHTML = '<p>Zadne specialni turnaje</p>';
        return;
    }

    let html = '';
    upcomingSpecial.forEach(tournament => {
        const tournamentDate = new Date(tournament.date);
        const formattedDate = tournamentDate.toLocaleDateString('cs-CZ');

        html += `
            <div class="special-tournament-item">
                <div class="special-tournament-icon">${tournament.icon}</div>
                <div class="special-tournament-info">
                    <h5>${tournament.name}</h5>
                    <p>${tournament.description} - ${formattedDate}</p>
                </div>
            </div>
        `;
    });

    specialContainer.innerHTML = html;
}
