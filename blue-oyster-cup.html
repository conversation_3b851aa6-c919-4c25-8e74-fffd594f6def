<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blue Oyster Cup - Bruntálská šipková liga</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        /* Dropdown menu styling */
        .dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-menu {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background-color: #dc143c;
            min-width: 180px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1;
            border-radius: 5px;
            margin-top: 5px;
        }

        .dropdown-menu li {
            margin: 0;
        }

        .dropdown-menu a {
            color: #ffffff;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            border-radius: 0;
        }

        .dropdown-menu a:hover {
            background-color: #06cef9;
        }

        .dropdown:hover .dropdown-menu {
            display: block;
        }

        @media (max-width: 768px) {
            .dropdown-menu {
                position: static;
                display: block;
                box-shadow: none;
                background-color: transparent;
                margin-top: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Hlavička -->
    <header>
        <div class="container">
            <h1>Bruntálská šipková liga</h1>
            <nav>
                <ul>
                    <li><a href="index.html">Domů</a></li>
                    <li><a href="hraci.html">Hráči</a></li>
                    <li class="dropdown">
                        <a href="liga.html">Liga</a>
                        <ul class="dropdown-menu">
                            <li><a href="liga.html#tabulka">Tabulka ligy</a></li>
                            <li><a href="liga.html#rozpis">Rozpis zápasů</a></li>
                            <li><a href="liga.html#pravidla">Pravidla</a></li>
                        </ul>
                    </li>
                    <li><a href="blue-oyster-cup.html" class="active">Blue Oyster Cup</a></li>
                    <li><a href="rosti-cup.html">Roští Cup</a></li>
                    <li><a href="sob-cup.html">Sob Cup</a></li>
                    <li><a href="specialni.html">Speciální turnaje</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Obsah -->
    <main>
        <div class="container">
            <!-- Informace o turnaji -->
            <section>
                <h2>Blue Oyster Cup</h2>
                <p>Turnaje série Blue Oyster Cup se konají každé úterý.</p>
            </section>

            <!-- Tabulka BOC -->
            <section>
                <h2>Tabulka Blue Oyster Cup</h2>
                <p>Celková tabulka hráčů s body a počtem účastí:</p>

                <div id="boc-standings">
                    <p>Načítání tabulky...</p>
                </div>
            </section>

            <!-- Seznam turnajů -->
            <section>
                <h2>Seznam turnajů Blue Oyster Cup</h2>
                <div id="boc-tournaments">
                    <p>Načítání turnajů...</p>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>&copy; 2025 Bruntálská šipková liga</p>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadBOCTournaments();
            loadBOCStandings();
        });

        function loadBOCTournaments() {
            const container = document.getElementById('boc-tournaments');

            fetch('php/get-category-standings.php?category=Blue Oyster Cup&limit=0')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.recent_tournaments) {
                        displayTournaments(data.recent_tournaments);
                    } else {
                        container.innerHTML = '<p>Žádné turnaje k zobrazení nebo data nejsou synchronizována.</p>';
                    }
                })
                .catch(error => {
                    console.error('Chyba při načítání turnajů:', error);
                    container.innerHTML = '<p>Chyba při načítání turnajů. Zkuste to později.</p>';
                });
        }

        function displayTournaments(tournaments) {
            const container = document.getElementById('boc-tournaments');

            if (!tournaments || tournaments.length === 0) {
                container.innerHTML = '<p>Žádné turnaje k zobrazení</p>';
                return;
            }

            let html = `
                <table>
                    <thead>
                        <tr>
                            <th>Název</th>
                            <th>Datum</th>
                            <th>Účastníci</th>
                            <th>Vítěz</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            tournaments.forEach(tournament => {
                const date = tournament.start_date
                    ? new Date(tournament.start_date).toLocaleDateString('cs-CZ')
                    : 'N/A';

                const status = getStatusText(tournament.status);

                html += `
                    <tr>
                        <td>${tournament.name}</td>
                        <td>${date}</td>
                        <td>${tournament.participants_count || 0}</td>
                        <td>${tournament.winner || 'N/A'}</td>
                        <td>${status}</td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                </table>
            `;

            container.innerHTML = html;
        }

        function getStatusText(status) {
            switch (status) {
                case 'pending':
                    return 'Čeká na zahájení';
                case 'underway':
                    return 'Probíhá';
                case 'complete':
                    return 'Dokončeno';
                default:
                    return 'Neurčeno';
            }
        }

        function loadBOCStandings() {
            const container = document.getElementById('boc-standings');

            // Použijeme existující API pro hráče a turnaje
            Promise.all([
                fetch('php/get-players-stats.php'),
                fetch('php/get-challonge-tournaments.php')
            ])
            .then(responses => Promise.all(responses.map(r => r.json())))
            .then(([players, tournaments]) => {
                // Filtrujeme BOC turnaje
                const bocTournaments = tournaments.filter(t =>
                    t.tournament.name.toLowerCase().includes('boc') ||
                    t.tournament.name.toLowerCase().includes('blue oyster')
                );

                // Vytvoříme statistiky
                const stats = {
                    total_tournaments: bocTournaments.length,
                    unique_players: players.length,
                    total_points_awarded: 0
                };

                displayStandings(players.slice(0, 20), stats);
            })
            .catch(error => {
                console.error('Chyba při načítání tabulky:', error);
                container.innerHTML = '<p>Chyba při načítání tabulky. Zkuste to později.</p>';
            });
        }

        function displayStandings(standings, stats) {
            const container = document.getElementById('boc-standings');

            if (!standings || standings.length === 0) {
                container.innerHTML = '<p>Žádní hráči k zobrazení</p>';
                return;
            }

            let html = '';

            // Statistiky
            if (stats) {
                html += `
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 20px 0;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #dc143c;">${stats.total_tournaments || 0}</div>
                            <div style="color: #666;">Turnajů</div>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #dc143c;">${stats.unique_players || 0}</div>
                            <div style="color: #666;">Hráčů</div>
                        </div>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #dc143c;">${stats.total_points_awarded || 0}</div>
                            <div style="color: #666;">Bodů</div>
                        </div>
                    </div>
                `;
            }

            // Tabulka top 20
            html += `
                <table style="width: 100%; border-collapse: collapse; margin: 20px 0; background: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                    <thead>
                        <tr style="background: #dc143c; color: white;">
                            <th style="padding: 12px 8px; text-align: center;">Pořadí</th>
                            <th style="padding: 12px 8px; text-align: left;">Jméno</th>
                            <th style="padding: 12px 8px; text-align: center;">Účasti</th>
                            <th style="padding: 12px 8px; text-align: center;">Body</th>
                            <th style="padding: 12px 8px; text-align: center;">Průměr</th>
                            <th style="padding: 12px 8px; text-align: center;">Výhry</th>
                            <th style="padding: 12px 8px; text-align: center;">Pódium</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            standings.slice(0, 20).forEach((player, index) => {
                // Medaile pro top 3
                let medal = '';
                if (player.position === 1) medal = '🥇 ';
                else if (player.position === 2) medal = '🥈 ';
                else if (player.position === 3) medal = '🥉 ';

                html += `
                    <tr style="border-bottom: 1px solid #ddd;" onmouseover="this.style.background='#f5f5f5'" onmouseout="this.style.background='white'">
                        <td style="padding: 12px 8px; text-align: center; font-weight: bold; color: #dc143c;">${medal}${player.position}.</td>
                        <td style="padding: 12px 8px; font-weight: bold;">${player.name}</td>
                        <td style="padding: 12px 8px; text-align: center;">${player.tournaments_played}</td>
                        <td style="padding: 12px 8px; text-align: center; font-weight: bold; color: #007bff;">${player.total_points}</td>
                        <td style="padding: 12px 8px; text-align: center;">${player.avg_points}</td>
                        <td style="padding: 12px 8px; text-align: center;">${player.wins}</td>
                        <td style="padding: 12px 8px; text-align: center;">${player.podiums}</td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                </table>
                <p style="text-align: center; margin: 20px 0;">
                    <em>Zobrazeno top 20 hráčů. Kompletní tabulka bude dostupná po synchronizaci s Challonge.</em>
                </p>
            `;

            container.innerHTML = html;
        }
    </script>
</body>
</html>
