<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Challonge API - Administrace Bruntálské šipkové ligy</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body class="admin-page">
    <header class="admin-header">
        <div class="container">
            <div class="admin-nav">
                <h1>Administrace Bruntálské šipkové ligy</h1>
                <ul>
                    <li><a href="index.html">Dashboard</a></li>
                    <li><a href="challonge.html" class="active">Challonge</a></li>
                    <li><a href="pravidla.html">Pravidla</a></li>
                </ul>
                <div class="admin-user-info">
                    <span id="user-name">Uživatel</span>
                    <a href="#" id="logout-btn" class="btn btn-sm btn-secondary">Odhlásit se</a>
                </div>
            </div>
        </div>
    </header>

    <main class="admin-container">
        <div class="container">
            <div id="admin-message"></div>

            <div class="admin-section">
                <h2>Nastavení Challonge API</h2>
                <div class="admin-form">
                    <div class="form-group">
                        <label for="api-key">API klíč</label>
                        <input type="text" id="api-key" name="api-key" placeholder="Zadejte API klíč">
                    </div>
                    <div class="form-group">
                        <label for="username">Uživatelské jméno</label>
                        <input type="text" id="username" name="username" placeholder="Zadejte uživatelské jméno">
                    </div>
                    <div class="form-actions">
                        <button id="save-settings-btn" class="btn btn-primary">Uložit nastavení</button>
                        <button id="test-connection-btn" class="btn btn-secondary">Otestovat připojení</button>
                    </div>
                </div>
            </div>

            <div class="admin-section">
                <h2>Testování a synchronizace</h2>
                <div class="admin-form">
                    <p>Poslední synchronizace: <span id="last-sync-time">Načítání...</span></p>
                    <div class="form-actions">
                        <button id="test-sync-btn" class="btn btn-secondary">Otestovat systém</button>
                        <button id="sync-data-btn" class="btn btn-primary">Kompletní synchronizace</button>
                        <button id="load-stats-btn" class="btn btn-info">Načíst statistiky</button>
                    </div>
                </div>
                <div id="sync-result" class="mt-3"></div>
            </div>

            <div class="admin-section">
                <h2>Statistiky Challonge</h2>
                <div class="admin-form">
                    <div class="form-group">
                        <label for="stats-category">Kategorie</label>
                        <select id="stats-category" name="stats-category">
                            <option value="all">Všechny kategorie</option>
                            <option value="Blue Oyster Cup">Blue Oyster Cup</option>
                            <option value="Sob Cup">Sob Cup</option>
                            <option value="Roští Cup">Roští Cup</option>
                            <option value="Speciální turnaje">Speciální turnaje</option>
                        </select>
                    </div>
                </div>

                <div id="challonge-stats" class="stats-container"></div>
            </div>

            <div class="admin-section">
                <h2>Turnaje z Challonge</h2>
                <div id="challonge-tournaments">
                    <p>Načítání turnajů...</p>
                </div>
            </div>
        </div>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Kontrola, zda je uživatel přihlášen
            checkLogin();

            // Načtení nastavení Challonge API
            loadChallongeSettings();

            // Načtení času poslední synchronizace
            loadLastSyncTime();

            // Načtení turnajů z Challonge
            loadChallongeTournaments();

            // Obsluha odhlášení
            document.getElementById('logout-btn').addEventListener('click', function(e) {
                e.preventDefault();
                logout();
            });

            // Obsluha uložení nastavení
            document.getElementById('save-settings-btn').addEventListener('click', function() {
                saveChallongeSettings();
            });

            // Obsluha testování připojení
            document.getElementById('test-connection-btn').addEventListener('click', function() {
                testChallongeConnection();
            });

            // Obsluha testování systému
            document.getElementById('test-sync-btn').addEventListener('click', function() {
                testSyncSystem();
            });

            // Obsluha synchronizace dat
            document.getElementById('sync-data-btn').addEventListener('click', function() {
                syncChallongeData();
            });

            // Obsluha načítání statistik
            document.getElementById('load-stats-btn').addEventListener('click', function() {
                loadChallongeStats();
            });
        });

        /**
         * Kontrola, zda je uživatel přihlášen
         */
        function checkLogin() {
            fetch('../php/check-login.php')
                .then(response => response.json())
                .then(data => {
                    if (!data.logged_in) {
                        // Přesměrování na přihlašovací stránku
                        window.location.href = 'login.html';
                    } else {
                        // Zobrazení jména přihlášeného uživatele
                        document.getElementById('user-name').textContent = data.username;
                    }
                })
                .catch(error => {
                    console.error('Chyba při kontrole přihlášení:', error);
                    window.location.href = 'login.html';
                });
        }

        /**
         * Odhlášení uživatele
         */
        function logout() {
            fetch('../php/logout.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = 'login.html';
                    } else {
                        document.getElementById('admin-message').innerHTML = `<div class="alert alert-error">${data.message}</div>`;
                    }
                })
                .catch(error => {
                    console.error('Chyba při odhlašování:', error);
                    document.getElementById('admin-message').innerHTML = '<div class="alert alert-error">Chyba při odhlašování. Zkuste to prosím znovu.</div>';
                });
        }

        /**
         * Načtení nastavení Challonge API
         */
        function loadChallongeSettings() {
            fetch('../php/get-challonge-settings.php')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        document.getElementById('admin-message').innerHTML = `<div class="alert alert-error">${data.error}</div>`;
                        return;
                    }

                    // Zobrazení nastavení
                    document.getElementById('api-key').value = data.api_key || '';
                    document.getElementById('username').value = data.username || '';
                })
                .catch(error => {
                    console.error('Chyba při načítání nastavení:', error);
                    document.getElementById('admin-message').innerHTML = '<div class="alert alert-error">Chyba při načítání nastavení. Zkuste to prosím znovu.</div>';
                });
        }

        /**
         * Uložení nastavení Challonge API
         */
        function saveChallongeSettings() {
            const apiKey = document.getElementById('api-key').value;
            const username = document.getElementById('username').value;

            fetch('../php/update-challonge-settings.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `api_key=${encodeURIComponent(apiKey)}&username=${encodeURIComponent(username)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('admin-message').innerHTML = '<div class="alert alert-success">Nastavení bylo úspěšně uloženo</div>';
                } else {
                    document.getElementById('admin-message').innerHTML = `<div class="alert alert-error">${data.message}</div>`;
                }
            })
            .catch(error => {
                console.error('Chyba při ukládání nastavení:', error);
                document.getElementById('admin-message').innerHTML = '<div class="alert alert-error">Chyba při ukládání nastavení. Zkuste to prosím znovu.</div>';
            });
        }

        /**
         * Testování připojení k Challonge API
         */
        function testChallongeConnection() {
            document.getElementById('admin-message').innerHTML = '<div class="alert alert-info">Testování připojení...</div>';

            fetch('../php/test-challonge-connection.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('admin-message').innerHTML = '<div class="alert alert-success">Připojení k Challonge API je funkční</div>';
                    } else {
                        document.getElementById('admin-message').innerHTML = `<div class="alert alert-error">${data.message}</div>`;
                    }
                })
                .catch(error => {
                    console.error('Chyba při testování připojení:', error);
                    document.getElementById('admin-message').innerHTML = '<div class="alert alert-error">Chyba při testování připojení. Zkuste to prosím znovu.</div>';
                });
        }

        /**
         * Načtení času poslední synchronizace
         */
        function loadLastSyncTime() {
            fetch('../php/get-last-sync-time.php')
                .then(response => response.json())
                .then(data => {
                    if (data.last_sync_time) {
                        const syncDate = new Date(data.last_sync_time);
                        document.getElementById('last-sync-time').textContent = syncDate.toLocaleString('cs-CZ');
                    } else {
                        document.getElementById('last-sync-time').textContent = 'Nikdy';
                    }
                })
                .catch(error => {
                    console.error('Chyba při načítání času poslední synchronizace:', error);
                    document.getElementById('last-sync-time').textContent = 'Chyba při načítání';
                });
        }

        /**
         * Testování systému před synchronizací
         */
        function testSyncSystem() {
            document.getElementById('admin-message').innerHTML = '<div class="alert alert-info">Testování systému...</div>';
            document.getElementById('sync-result').innerHTML = '<p>Probíhá testování systému...</p>';

            fetch('../php/test-challonge-sync.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('admin-message').innerHTML = '<div class="alert alert-success">Všechny testy prošly úspěšně</div>';

                        let html = '<h3>Výsledky testování</h3>';

                        Object.values(data.tests).forEach(test => {
                            const statusClass = test.status === 'success' ? 'success' :
                                              test.status === 'warning' ? 'warning' : 'error';

                            html += `
                                <div class="test-result ${statusClass}">
                                    <h4>${test.name}</h4>
                                    <p><strong>Status:</strong> ${test.status}</p>
                                    ${test.message ? `<p>${test.message}</p>` : ''}
                                </div>
                            `;
                        });

                        if (data.ready_for_sync) {
                            html += '<div class="alert alert-success">Systém je připraven pro synchronizaci!</div>';
                        }

                        document.getElementById('sync-result').innerHTML = html;
                    } else {
                        document.getElementById('admin-message').innerHTML = `<div class="alert alert-error">${data.message}</div>`;
                        document.getElementById('sync-result').innerHTML = '';
                    }
                })
                .catch(error => {
                    console.error('Chyba při testování:', error);
                    document.getElementById('admin-message').innerHTML = '<div class="alert alert-error">Chyba při testování systému.</div>';
                    document.getElementById('sync-result').innerHTML = '';
                });
        }

        /**
         * Synchronizace dat z Challonge API
         */
        function syncChallongeData() {
            document.getElementById('admin-message').innerHTML = '<div class="alert alert-info">Synchronizace dat...</div>';
            document.getElementById('sync-result').innerHTML = '<p>Probíhá kompletní synchronizace dat...</p>';

            fetch('../php/sync-and-evaluate-tournaments.php')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        document.getElementById('admin-message').innerHTML = '<div class="alert alert-success">Synchronizace byla úspěšně dokončena</div>';

                        let html = '<h3>Výsledky synchronizace</h3>';

                        if (data.stats) {
                            html += `
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <div class="card-value">${data.stats.tournaments_processed}</div>
                                        <div class="card-label">Zpracovaných turnajů</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="card-value">${data.stats.tournaments_new}</div>
                                        <div class="card-label">Nových turnajů</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="card-value">${data.stats.participants_processed}</div>
                                        <div class="card-label">Zpracovaných hráčů</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="card-value">${data.stats.points_awarded}</div>
                                        <div class="card-label">Udělených bodů</div>
                                    </div>
                                </div>
                            `;

                            // Zobrazení kategorií
                            html += '<h4>Turnaje podle kategorií:</h4><ul>';
                            Object.entries(data.stats.categories).forEach(([category, count]) => {
                                html += `<li>${category}: ${count} turnajů</li>`;
                            });
                            html += '</ul>';

                            // Nejisté turnaje
                            if (data.stats.uncertain_tournaments.length > 0) {
                                html += '<h4>Turnaje vyžadující kontrolu kategorie:</h4><ul>';
                                data.stats.uncertain_tournaments.forEach(tournament => {
                                    html += `<li><a href="${tournament.url}" target="_blank">${tournament.name}</a> (navrženo: ${tournament.suggested_category})</li>`;
                                });
                                html += '</ul>';
                            }
                        }

                        document.getElementById('sync-result').innerHTML = html;

                        // Aktualizace času poslední synchronizace
                        loadLastSyncTime();

                        // Aktualizace seznamu turnajů
                        loadChallongeTournaments();
                    } else {
                        document.getElementById('admin-message').innerHTML = `<div class="alert alert-error">${data.message}</div>`;
                        document.getElementById('sync-result').innerHTML = '';
                    }
                })
                .catch(error => {
                    console.error('Chyba při synchronizaci dat:', error);
                    document.getElementById('admin-message').innerHTML = '<div class="alert alert-error">Chyba při synchronizaci dat. Zkuste to prosím znovu.</div>';
                    document.getElementById('sync-result').innerHTML = '';
                });
        }

        /**
         * Načtení statistik z Challonge
         */
        function loadChallongeStats() {
            const category = document.getElementById('stats-category').value;
            const statsContainer = document.getElementById('challonge-stats');

            statsContainer.innerHTML = '<p>Načítání statistik...</p>';

            fetch(`../php/get-challonge-stats.php?category=${encodeURIComponent(category)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let html = '<h3>Statistiky Challonge</h3>';

                        // Celkové statistiky
                        if (data.stats.overall) {
                            const overall = data.stats.overall;
                            html += `
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <div class="card-value">${overall.total_tournaments}</div>
                                        <div class="card-label">Celkem turnajů</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="card-value">${overall.unique_players}</div>
                                        <div class="card-label">Unikátních hráčů</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="card-value">${overall.total_points_awarded}</div>
                                        <div class="card-label">Celkem bodů</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="card-value">${Math.round(overall.avg_tournament_size)}</div>
                                        <div class="card-label">Průměrná velikost turnaje</div>
                                    </div>
                                </div>
                            `;
                        }

                        // Top hráči
                        if (data.stats.top_players && data.stats.top_players.length > 0) {
                            html += '<h4>Nejlepší hráči:</h4>';
                            html += '<table class="admin-table"><thead><tr><th>Hráč</th><th>Turnaje</th><th>Body</th><th>Průměr</th></tr></thead><tbody>';
                            data.stats.top_players.slice(0, 10).forEach(player => {
                                html += `
                                    <tr>
                                        <td>${player.name}</td>
                                        <td>${player.tournaments_played}</td>
                                        <td>${player.total_points}</td>
                                        <td>${Math.round(player.avg_points)}</td>
                                    </tr>
                                `;
                            });
                            html += '</tbody></table>';
                        }

                        statsContainer.innerHTML = html;
                    } else {
                        statsContainer.innerHTML = `<p>Chyba při načítání statistik: ${data.message}</p>`;
                    }
                })
                .catch(error => {
                    console.error('Chyba při načítání statistik:', error);
                    statsContainer.innerHTML = '<p>Chyba při načítání statistik</p>';
                });
        }

        /**
         * Načtení turnajů z Challonge API
         */
        function loadChallongeTournaments() {
            fetch('../php/get-challonge-tournaments.php')
                .then(response => response.json())
                .then(data => {
                    const tournamentsContainer = document.getElementById('challonge-tournaments');

                    if (!data || data.length === 0) {
                        tournamentsContainer.innerHTML = '<p>Žádné turnaje k zobrazení</p>';
                        return;
                    }

                    // Zobrazení turnajů z Challonge
                    let html = `
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>Název</th>
                                    <th>Datum zahájení</th>
                                    <th>Status</th>
                                    <th>Akce</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;

                    data.forEach(tournamentData => {
                        const tournament = tournamentData.tournament;
                        const startDate = tournament.started_at
                            ? new Date(tournament.started_at).toLocaleDateString('cs-CZ')
                            : 'Neurčeno';
                        const status = getStatusText(tournament.state);

                        html += `
                            <tr>
                                <td>${tournament.name}</td>
                                <td>${startDate}</td>
                                <td>${status}</td>
                                <td>
                                    <div class="actions">
                                        <a href="${tournament.full_challonge_url}" class="btn btn-sm btn-primary" target="_blank">Zobrazit na Challonge</a>
                                    </div>
                                </td>
                            </tr>
                        `;
                    });

                    html += `
                            </tbody>
                        </table>
                    `;

                    tournamentsContainer.innerHTML = html;
                })
                .catch(error => {
                    console.error('Chyba při načítání turnajů:', error);
                    document.getElementById('challonge-tournaments').innerHTML = '<p>Chyba při načítání turnajů</p>';
                });
        }

        /**
         * Převede status turnaje na čitelný text
         *
         * @param {string} status Status turnaje
         * @return {string} Čitelný text statusu
         */
        function getStatusText(status) {
            switch (status) {
                case 'pending':
                    return 'Čeká na zahájení';
                case 'underway':
                    return 'Probíhá';
                case 'complete':
                    return 'Dokončeno';
                default:
                    return 'Neurčeno';
            }
        }
    </script>
</body>
</html>
