<?php
/**
 * Skript pro aktualizaci databáze pro Challonge integraci
 * Přidá chyběj<PERSON><PERSON><PERSON> sloupce a tabulky
 */

// Připojení k databázi
require_once 'db-connection.php';

// Nastavení hlavičky pro JSON odpověď
header('Content-Type: application/json');

// Spuštění session
session_start();

// Kontrola, zda je uživatel přihlášen
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header('HTTP/1.1 401 Unauthorized');
    echo json_encode(['success' => false, 'message' => 'Nejste přihlášen']);
    exit;
}

try {
    $updates = [];
    $errors = [];
    
    // 1. Přidání sloupce participants_count do tabulky tournaments
    try {
        $pdo->exec("ALTER TABLE tournaments ADD COLUMN participants_count INT DEFAULT 0");
        $updates[] = "Přidán sloupec participants_count do tabulky tournaments";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            $updates[] = "Sloupec participants_count už existuje v tabulce tournaments";
        } else {
            $errors[] = "Chyba při přidávání sloupce participants_count: " . $e->getMessage();
        }
    }
    
    // 2. Přidání sloupce points do tabulky tournament_participants
    try {
        $pdo->exec("ALTER TABLE tournament_participants ADD COLUMN points INT DEFAULT 0");
        $updates[] = "Přidán sloupec points do tabulky tournament_participants";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            $updates[] = "Sloupec points už existuje v tabulce tournament_participants";
        } else {
            $errors[] = "Chyba při přidávání sloupce points: " . $e->getMessage();
        }
    }
    
    // 3. Přidání sloupce name do tabulky tournament_participants
    try {
        $pdo->exec("ALTER TABLE tournament_participants ADD COLUMN name VARCHAR(255) NOT NULL DEFAULT ''");
        $updates[] = "Přidán sloupec name do tabulky tournament_participants";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            $updates[] = "Sloupec name už existuje v tabulce tournament_participants";
        } else {
            $errors[] = "Chyba při přidávání sloupce name: " . $e->getMessage();
        }
    }
    
    // 4. Změna player_id na nullable
    try {
        $pdo->exec("ALTER TABLE tournament_participants MODIFY COLUMN player_id INT NULL");
        $updates[] = "Sloupec player_id změněn na nullable";
    } catch (PDOException $e) {
        $errors[] = "Chyba při změně sloupce player_id: " . $e->getMessage();
    }
    
    // 5. Změna start_date na DATETIME
    try {
        $pdo->exec("ALTER TABLE tournaments MODIFY COLUMN start_date DATETIME NULL");
        $updates[] = "Sloupec start_date změněn na DATETIME";
    } catch (PDOException $e) {
        $errors[] = "Chyba při změně sloupce start_date: " . $e->getMessage();
    }
    
    // 6. Přidání UNIQUE indexu na challonge_id
    try {
        $pdo->exec("ALTER TABLE tournaments ADD CONSTRAINT unique_challonge_id UNIQUE (challonge_id)");
        $updates[] = "Přidán UNIQUE index na challonge_id";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            $updates[] = "UNIQUE index na challonge_id už existuje";
        } else {
            $errors[] = "Chyba při přidávání UNIQUE indexu na challonge_id: " . $e->getMessage();
        }
    }
    
    // 7. Přidání UNIQUE indexu na tournament_participants
    try {
        $pdo->exec("ALTER TABLE tournament_participants ADD CONSTRAINT unique_participant UNIQUE (tournament_id, challonge_participant_id)");
        $updates[] = "Přidán UNIQUE index na tournament_participants";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            $updates[] = "UNIQUE index na tournament_participants už existuje";
        } else {
            $errors[] = "Chyba při přidávání UNIQUE indexu na tournament_participants: " . $e->getMessage();
        }
    }
    
    // 8. Kontrola a vložení kategorií
    $stmt = $pdo->query("SELECT COUNT(*) FROM tournament_categories");
    $categoryCount = $stmt->fetchColumn();
    
    if ($categoryCount == 0) {
        $pdo->exec("
            INSERT INTO tournament_categories (name, description) VALUES
            ('Blue Oyster Cup', 'Úterní turnaje Blue Oyster Cup'),
            ('Sob Cup', 'Čtvrteční turnaje Sob Cup'),
            ('Roští Cup', 'Střední turnaje Roští Cup'),
            ('Speciální turnaje', 'Ostatní a speciální turnaje')
        ");
        $updates[] = "Vloženy základní kategorie turnajů";
    } else {
        $updates[] = "Kategorie turnajů už existují ($categoryCount kategorií)";
    }
    
    // 9. Kontrola a vložení nastavení
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM settings WHERE setting_key = ?");
    
    // API klíč
    $stmt->execute(['challonge_api_key']);
    if ($stmt->fetchColumn() == 0) {
        $pdo->exec("INSERT INTO settings (setting_key, setting_value) VALUES ('challonge_api_key', 'adbPU7e6QlOzIvxzSDnohavu61YwTNhl6FIDkF3H')");
        $updates[] = "Vložen Challonge API klíč";
    } else {
        $updates[] = "Challonge API klíč už existuje";
    }
    
    // Username
    $stmt->execute(['challonge_username']);
    if ($stmt->fetchColumn() == 0) {
        $pdo->exec("INSERT INTO settings (setting_key, setting_value) VALUES ('challonge_username', 'Rezexil')");
        $updates[] = "Vložen Challonge username";
    } else {
        $updates[] = "Challonge username už existuje";
    }
    
    // Last sync time
    $stmt->execute(['last_sync_time']);
    if ($stmt->fetchColumn() == 0) {
        $pdo->exec("INSERT INTO settings (setting_key, setting_value) VALUES ('last_sync_time', NULL)");
        $updates[] = "Vložen last_sync_time";
    } else {
        $updates[] = "Last_sync_time už existuje";
    }
    
    // 10. Získání aktuálního stavu tabulek
    $tableStats = [];
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM tournament_categories");
    $tableStats['tournament_categories'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM tournaments");
    $tableStats['tournaments'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM tournament_participants");
    $tableStats['tournament_participants'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM settings");
    $tableStats['settings'] = $stmt->fetchColumn();
    
    // 11. Získání kategorií
    $stmt = $pdo->query("SELECT * FROM tournament_categories");
    $categories = $stmt->fetchAll();
    
    // 12. Získání nastavení
    $stmt = $pdo->query("SELECT * FROM settings WHERE setting_key LIKE 'challonge%' OR setting_key = 'last_sync_time'");
    $settings = $stmt->fetchAll();
    
    // Vrácení výsledků
    echo json_encode([
        'success' => true,
        'message' => 'Aktualizace databáze dokončena',
        'updates' => $updates,
        'errors' => $errors,
        'table_stats' => $tableStats,
        'categories' => $categories,
        'settings' => $settings
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Chyba při aktualizaci databáze: ' . $e->getMessage()
    ]);
}
?>
